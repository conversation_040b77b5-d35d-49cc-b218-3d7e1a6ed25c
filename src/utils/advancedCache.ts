import { TimeBasedCache, CacheManager } from './cache.js';
import { globalPerformanceMonitor } from './performance.js';

/**
 * Cache invalidation strategies
 */
export enum CacheInvalidationStrategy {
  TIME_BASED = 'time-based',
  DEPENDENCY_BASED = 'dependency-based',
  EVENT_BASED = 'event-based',
  MANUAL = 'manual'
}

/**
 * Cache warming configuration
 */
export interface CacheWarmingConfig {
  enabled: boolean;
  strategies: string[];
  interval: number; // milliseconds
  maxConcurrency: number;
}

/**
 * Cache dependency tracking
 */
export interface CacheDependency {
  type: 'file' | 'directory' | 'project' | 'command';
  path: string;
  lastModified?: Date;
  checksum?: string;
}

/**
 * Advanced cache entry with dependency tracking
 */
export interface AdvancedCacheEntry<T> {
  value: T;
  timestamp: Date;
  ttl: number;
  dependencies: CacheDependency[];
  accessCount: number;
  lastAccessed: Date;
  tags: string[];
  metadata: Record<string, unknown>;
}

/**
 * Cache statistics for monitoring
 */
export interface CacheStatistics {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
  hitRate: number;
  averageAccessTime: number;
  memoryUsage: number;
}

/**
 * Advanced cache with intelligent invalidation and warming
 */
export class AdvancedCache<T> {
  private cache = new Map<string, AdvancedCacheEntry<T>>();
  private dependencyMap = new Map<string, Set<string>>();
  private tagMap = new Map<string, Set<string>>();
  private statistics: CacheStatistics = {
    hits: 0,
    misses: 0,
    evictions: 0,
    size: 0,
    hitRate: 0,
    averageAccessTime: 0,
    memoryUsage: 0
  };

  constructor(
    private maxSize: number = 1000,
    private defaultTtl: number = 300000, // 5 minutes
    private warmingConfig?: CacheWarmingConfig
  ) {
    if (warmingConfig?.enabled) {
      this.startCacheWarming();
    }
  }

  /**
   * Get value from cache with dependency checking
   */
  async get(key: string): Promise<T | undefined> {
    const startTime = performance.now();
    
    try {
      const entry = this.cache.get(key);
      
      if (!entry) {
        this.statistics.misses++;
        return undefined;
      }

      // Check if entry is expired
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.cleanupDependencies(key);
        this.statistics.misses++;
        this.statistics.evictions++;
        return undefined;
      }

      // Check dependencies for invalidation
      if (await this.areDependenciesInvalid(entry.dependencies)) {
        this.cache.delete(key);
        this.cleanupDependencies(key);
        this.statistics.misses++;
        this.statistics.evictions++;
        return undefined;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = new Date();
      this.statistics.hits++;

      return entry.value;
    } finally {
      const duration = performance.now() - startTime;
      this.updateAccessTime(duration);
    }
  }

  /**
   * Set value in cache with dependencies and tags
   */
  async set(
    key: string,
    value: T,
    options: {
      ttl?: number;
      dependencies?: CacheDependency[];
      tags?: string[];
      metadata?: Record<string, unknown>;
    } = {}
  ): Promise<void> {
    const {
      ttl = this.defaultTtl,
      dependencies = [],
      tags = [],
      metadata = {}
    } = options;

    // Evict if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    const entry: AdvancedCacheEntry<T> = {
      value,
      timestamp: new Date(),
      ttl,
      dependencies,
      accessCount: 0,
      lastAccessed: new Date(),
      tags,
      metadata
    };

    this.cache.set(key, entry);
    this.updateDependencyMap(key, dependencies);
    this.updateTagMap(key, tags);
    this.updateStatistics();
  }

  /**
   * Invalidate cache entries by dependency
   */
  async invalidateByDependency(dependency: CacheDependency): Promise<void> {
    const dependencyKey = this.getDependencyKey(dependency);
    const affectedKeys = this.dependencyMap.get(dependencyKey);
    
    if (affectedKeys) {
      for (const key of affectedKeys) {
        this.cache.delete(key);
        this.cleanupDependencies(key);
        this.statistics.evictions++;
      }
    }
    
    this.updateStatistics();
  }

  /**
   * Invalidate cache entries by tag
   */
  async invalidateByTag(tag: string): Promise<void> {
    const affectedKeys = this.tagMap.get(tag);
    
    if (affectedKeys) {
      for (const key of affectedKeys) {
        this.cache.delete(key);
        this.cleanupDependencies(key);
        this.statistics.evictions++;
      }
    }
    
    this.updateStatistics();
  }

  /**
   * Warm cache with frequently accessed data
   */
  async warmCache(warmingStrategies: Array<() => Promise<void>>): Promise<void> {
    const maxConcurrency = this.warmingConfig?.maxConcurrency || 3;
    const chunks = this.chunkArray(warmingStrategies, maxConcurrency);
    
    for (const chunk of chunks) {
      await Promise.allSettled(chunk.map(strategy => strategy()));
    }
  }

  /**
   * Get cache statistics
   */
  getStatistics(): CacheStatistics {
    this.updateStatistics();
    return { ...this.statistics };
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.dependencyMap.clear();
    this.tagMap.clear();
    this.statistics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      size: 0,
      hitRate: 0,
      averageAccessTime: 0,
      memoryUsage: 0
    };
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: AdvancedCacheEntry<T>): boolean {
    const now = Date.now();
    const entryTime = entry.timestamp.getTime();
    return (now - entryTime) > entry.ttl;
  }

  /**
   * Check if dependencies are invalid
   */
  private async areDependenciesInvalid(dependencies: CacheDependency[]): Promise<boolean> {
    for (const dependency of dependencies) {
      if (await this.isDependencyInvalid(dependency)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if a single dependency is invalid
   */
  private async isDependencyInvalid(dependency: CacheDependency): Promise<boolean> {
    try {
      const fs = await import('fs/promises');
      const stats = await fs.stat(dependency.path);
      
      if (dependency.lastModified) {
        return stats.mtime > dependency.lastModified;
      }
      
      return false;
    } catch (error) {
      // If file doesn't exist, dependency is invalid
      return true;
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed.getTime() < oldestTime) {
        oldestTime = entry.lastAccessed.getTime();
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.cleanupDependencies(oldestKey);
      this.statistics.evictions++;
    }
  }

  /**
   * Update dependency mapping
   */
  private updateDependencyMap(key: string, dependencies: CacheDependency[]): void {
    for (const dependency of dependencies) {
      const depKey = this.getDependencyKey(dependency);
      if (!this.dependencyMap.has(depKey)) {
        this.dependencyMap.set(depKey, new Set());
      }
      this.dependencyMap.get(depKey)!.add(key);
    }
  }

  /**
   * Update tag mapping
   */
  private updateTagMap(key: string, tags: string[]): void {
    for (const tag of tags) {
      if (!this.tagMap.has(tag)) {
        this.tagMap.set(tag, new Set());
      }
      this.tagMap.get(tag)!.add(key);
    }
  }

  /**
   * Cleanup dependencies for a key
   */
  private cleanupDependencies(key: string): void {
    // Remove from dependency map
    for (const [depKey, keys] of this.dependencyMap.entries()) {
      keys.delete(key);
      if (keys.size === 0) {
        this.dependencyMap.delete(depKey);
      }
    }

    // Remove from tag map
    for (const [tag, keys] of this.tagMap.entries()) {
      keys.delete(key);
      if (keys.size === 0) {
        this.tagMap.delete(tag);
      }
    }
  }

  /**
   * Get dependency key for mapping
   */
  private getDependencyKey(dependency: CacheDependency): string {
    return `${dependency.type}:${dependency.path}`;
  }

  /**
   * Update cache statistics
   */
  private updateStatistics(): void {
    this.statistics.size = this.cache.size;
    const total = this.statistics.hits + this.statistics.misses;
    this.statistics.hitRate = total > 0 ? this.statistics.hits / total : 0;
    
    // Estimate memory usage (rough calculation)
    this.statistics.memoryUsage = this.cache.size * 1024; // Rough estimate
  }

  /**
   * Update average access time
   */
  private updateAccessTime(duration: number): void {
    const currentAvg = this.statistics.averageAccessTime;
    const total = this.statistics.hits + this.statistics.misses;
    this.statistics.averageAccessTime = (currentAvg * (total - 1) + duration) / total;
  }

  /**
   * Start cache warming process
   */
  private startCacheWarming(): void {
    if (!this.warmingConfig?.enabled) return;

    setInterval(() => {
      // Implement cache warming logic here
      // This would be customized based on application needs
    }, this.warmingConfig.interval);
  }

  /**
   * Utility to chunk array for concurrent processing
   */
  private chunkArray<U>(array: U[], chunkSize: number): U[][] {
    const chunks: U[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

/**
 * Advanced cache manager with global cache coordination
 */
export class AdvancedCacheManager {
  private static caches = new Map<string, AdvancedCache<any>>();
  private static globalStatistics = {
    totalHits: 0,
    totalMisses: 0,
    totalEvictions: 0,
    totalMemoryUsage: 0
  };

  /**
   * Get or create an advanced cache
   */
  static getCache<T>(
    name: string,
    options: {
      maxSize?: number;
      defaultTtl?: number;
      warmingConfig?: CacheWarmingConfig;
    } = {}
  ): AdvancedCache<T> {
    if (!this.caches.has(name)) {
      const cache = new AdvancedCache<T>(
        options.maxSize,
        options.defaultTtl,
        options.warmingConfig
      );
      this.caches.set(name, cache);
    }
    
    return this.caches.get(name)!;
  }

  /**
   * Get global cache statistics
   */
  static getGlobalStatistics(): typeof AdvancedCacheManager.globalStatistics & {
    cacheCount: number;
    individualCaches: Record<string, CacheStatistics>;
  } {
    const individualCaches: Record<string, CacheStatistics> = {};
    let totalHits = 0;
    let totalMisses = 0;
    let totalEvictions = 0;
    let totalMemoryUsage = 0;

    for (const [name, cache] of this.caches.entries()) {
      const stats = cache.getStatistics();
      individualCaches[name] = stats;
      totalHits += stats.hits;
      totalMisses += stats.misses;
      totalEvictions += stats.evictions;
      totalMemoryUsage += stats.memoryUsage;
    }

    return {
      totalHits,
      totalMisses,
      totalEvictions,
      totalMemoryUsage,
      cacheCount: this.caches.size,
      individualCaches
    };
  }

  /**
   * Clear all caches
   */
  static clearAll(): void {
    for (const cache of this.caches.values()) {
      cache.clear();
    }
  }

  /**
   * Remove a cache
   */
  static removeCache(name: string): void {
    const cache = this.caches.get(name);
    if (cache) {
      cache.clear();
      this.caches.delete(name);
    }
  }
}
