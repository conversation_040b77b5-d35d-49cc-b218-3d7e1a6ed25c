/**
 * Performance monitoring and metrics collection utilities
 * Consolidated performance optimization and monitoring system
 */

import { <PERSON>BasedCache, CacheManager } from "./cache.js";

/**
 * Performance metric data
 */
export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

/**
 * Performance optimization configuration
 */
export interface OptimizationConfig {
  enableCaching?: boolean;
  enableMemoization?: boolean;
  enablePerformanceMonitoring?: boolean;
  cacheConfig?: {
    defaultTtl?: number;
    maxSize?: number;
  };
}

/**
 * Performance statistics
 */
export interface PerformanceStats {
  totalCalls: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  totalDuration: number;
  recentMetrics: PerformanceMetric[];
}

/**
 * Performance monitor for tracking operation performance
 */
export class PerformanceMonitor {
  private metrics = new Map<string, PerformanceMetric[]>();
  private readonly maxMetricsPerOperation: number;

  constructor(maxMetricsPerOperation = 100) {
    this.maxMetricsPerOperation = maxMetricsPerOperation;
  }

  /**
   * Start timing an operation
   */
  startTimer(operationName: string): PerformanceTimer {
    return new PerformanceTimer(operationName, this);
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const operationMetrics = this.metrics.get(metric.name)!;
    operationMetrics.push(metric);

    // Keep only the most recent metrics
    if (operationMetrics.length > this.maxMetricsPerOperation) {
      operationMetrics.shift();
    }
  }

  /**
   * Get statistics for an operation
   */
  getStats(operationName: string): PerformanceStats | null {
    const metrics = this.metrics.get(operationName);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const durations = metrics.map((m) => m.duration);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);

    return {
      totalCalls: metrics.length,
      averageDuration: totalDuration / metrics.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalDuration,
      recentMetrics: [...metrics].slice(-10), // Last 10 metrics
    };
  }

  /**
   * Get all operation names being monitored
   */
  getOperationNames(): string[] {
    return Array.from(this.metrics.keys());
  }

  /**
   * Get a summary of all operations
   */
  getAllStats(): Record<string, PerformanceStats> {
    const stats: Record<string, PerformanceStats> = {};

    for (const operationName of this.getOperationNames()) {
      const operationStats = this.getStats(operationName);
      if (operationStats) {
        stats[operationName] = operationStats;
      }
    }

    return stats;
  }

  /**
   * Clear metrics for an operation
   */
  clearMetrics(operationName: string): void {
    this.metrics.delete(operationName);
  }

  /**
   * Clear all metrics
   */
  clearAllMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Get slow operations (above threshold)
   */
  getSlowOperations(
    thresholdMs = 1000
  ): Array<{ name: string; stats: PerformanceStats }> {
    const slowOps: Array<{ name: string; stats: PerformanceStats }> = [];

    for (const operationName of this.getOperationNames()) {
      const stats = this.getStats(operationName);
      if (stats && stats.averageDuration > thresholdMs) {
        slowOps.push({ name: operationName, stats });
      }
    }

    return slowOps.sort(
      (a, b) => b.stats.averageDuration - a.stats.averageDuration
    );
  }
}

/**
 * Timer for measuring operation performance
 */
export class PerformanceTimer {
  private startTime: number;
  private endTime?: number;
  public metadata?: Record<string, unknown>;

  constructor(
    private operationName: string,
    private monitor: PerformanceMonitor,
    metadata?: Record<string, unknown>
  ) {
    this.startTime = performance.now();
    this.metadata = metadata;
  }

  /**
   * Stop the timer and record the metric
   */
  stop(additionalMetadata?: Record<string, unknown>): number {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;

    const metric: PerformanceMetric = {
      name: this.operationName,
      duration,
      timestamp: new Date(),
      metadata: { ...this.metadata, ...additionalMetadata },
    };

    this.monitor.recordMetric(metric);
    return duration;
  }

  /**
   * Get the current elapsed time without stopping the timer
   */
  getElapsed(): number {
    return performance.now() - this.startTime;
  }
}

/**
 * Decorator for automatically timing method calls
 */
export function timed(operationName?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const opName = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const timer = globalPerformanceMonitor.startTimer(opName);

      try {
        const result = await originalMethod.apply(this, args);
        timer.stop({ success: true });
        return result;
      } catch (error) {
        timer.stop({
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Utility function to measure async operations
 */
export async function measureAsync<T>(
  operationName: string,
  operation: () => Promise<T>,
  metadata?: Record<string, unknown>
): Promise<{ result: T; duration: number }> {
  const timer = globalPerformanceMonitor.startTimer(operationName);
  timer.metadata = metadata;

  try {
    const result = await operation();
    const duration = timer.stop({ success: true });
    return { result, duration };
  } catch (error) {
    const duration = timer.stop({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Utility function to measure sync operations
 */
export function measureSync<T>(
  operationName: string,
  operation: () => T,
  metadata?: Record<string, unknown>
): { result: T; duration: number } {
  const timer = globalPerformanceMonitor.startTimer(operationName);
  timer.metadata = metadata;

  try {
    const result = operation();
    const duration = timer.stop({ success: true });
    return { result, duration };
  } catch (error) {
    const duration = timer.stop({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Memoization cache for functions
 */
const memoCache = new Map<string, { value: any; timestamp: number }>();

/**
 * Memoize a function with TTL support
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl = 300000 // 5 minutes default
): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    const now = Date.now();
    const cached = memoCache.get(key);

    if (cached && now - cached.timestamp < ttl) {
      return cached.value;
    }

    const result = fn(...args);
    memoCache.set(key, { value: result, timestamp: now });
    return result;
  }) as T;
}

/**
 * Async memoization with promise caching
 */
export function memoizeAsync<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl = 300000 // 5 minutes default
): T {
  const cache = new Map<
    string,
    {
      promise: Promise<Awaited<ReturnType<T>>>;
      timestamp: number;
      resolved: boolean;
      value?: Awaited<ReturnType<T>>;
    }
  >();

  return (async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    const now = Date.now();
    const cached = cache.get(key);

    if (cached && now - cached.timestamp < ttl) {
      if (cached.resolved && cached.value !== undefined) {
        return cached.value;
      } else {
        return await cached.promise;
      }
    }

    const promise = fn(...args) as Promise<Awaited<ReturnType<T>>>;
    cache.set(key, { promise, timestamp: now, resolved: false });

    try {
      const result = await promise;
      cache.set(key, {
        promise,
        timestamp: now,
        resolved: true,
        value: result,
      });
      return result;
    } catch (error) {
      cache.delete(key); // Remove failed promises
      throw error;
    }
  }) as T;
}

/**
 * Performance-optimized file system operations
 */
export class OptimizedFileOperations {
  private static fileStatsCache = CacheManager.getCache("file_stats", {
    defaultTtl: 30000, // 30 seconds
    maxSize: 1000,
  });

  private static fileContentCache = CacheManager.getCache("file_content", {
    defaultTtl: 60000, // 1 minute
    maxSize: 100,
  });

  /**
   * Cached file stats check
   */
  static async getFileStats(filePath: string) {
    return this.fileStatsCache.getOrSet(filePath, async () => {
      const fs = await import("fs/promises");
      return fs.stat(filePath);
    });
  }

  /**
   * Cached file existence check
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await this.getFileStats(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Cached file content reading (for small files)
   */
  static async readFileContent(
    filePath: string,
    maxSize = 1024 * 1024
  ): Promise<string> {
    const stats = await this.getFileStats(filePath);

    if (stats.size > maxSize) {
      // Don't cache large files
      const fs = await import("fs/promises");
      return fs.readFile(filePath, "utf-8");
    }

    return this.fileContentCache.getOrSet(
      `${filePath}_${stats.mtime.getTime()}`,
      async () => {
        const fs = await import("fs/promises");
        return fs.readFile(filePath, "utf-8");
      }
    );
  }
}

/**
 * Global performance monitor instance
 */
export const globalPerformanceMonitor = new PerformanceMonitor();

/**
 * Performance reporting utilities
 */
export class PerformanceReporter {
  /**
   * Generate a performance report
   */
  static generateReport(
    monitor: PerformanceMonitor = globalPerformanceMonitor
  ): string {
    const allStats = monitor.getAllStats();
    const operationNames = Object.keys(allStats).sort();

    if (operationNames.length === 0) {
      return "No performance metrics available.";
    }

    let report = "Performance Report\n";
    report += "==================\n\n";

    for (const operationName of operationNames) {
      const stats = allStats[operationName];
      report += `Operation: ${operationName}\n`;
      report += `  Total Calls: ${stats.totalCalls}\n`;
      report += `  Average Duration: ${stats.averageDuration.toFixed(2)}ms\n`;
      report += `  Min Duration: ${stats.minDuration.toFixed(2)}ms\n`;
      report += `  Max Duration: ${stats.maxDuration.toFixed(2)}ms\n`;
      report += `  Total Duration: ${stats.totalDuration.toFixed(2)}ms\n\n`;
    }

    // Add slow operations section
    const slowOps = monitor.getSlowOperations(1000);
    if (slowOps.length > 0) {
      report += "Slow Operations (>1000ms average):\n";
      report += "===================================\n";
      for (const { name, stats } of slowOps) {
        report += `  ${name}: ${stats.averageDuration.toFixed(2)}ms average\n`;
      }
      report += "\n";
    }

    return report;
  }

  /**
   * Log performance warnings for slow operations
   */
  static logSlowOperations(
    monitor: PerformanceMonitor = globalPerformanceMonitor,
    thresholdMs = 5000
  ): void {
    const slowOps = monitor.getSlowOperations(thresholdMs);

    for (const { name, stats } of slowOps) {
      console.warn(
        `Slow operation detected: ${name} (${stats.averageDuration.toFixed(
          2
        )}ms average, ${stats.totalCalls} calls)`
      );
    }
  }
}

/**
 * Global performance optimizer
 */
export class PerformanceOptimizer {
  private static config: OptimizationConfig = {
    enableCaching: true,
    enableMemoization: true,
    enablePerformanceMonitoring: true,
    cacheConfig: {
      defaultTtl: 300000,
      maxSize: 1000,
    },
  };

  /**
   * Configure performance optimization settings
   */
  static configure(config: Partial<OptimizationConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  static getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  /**
   * Clear all caches
   */
  static clearAllCaches() {
    CacheManager.destroyAll();
  }

  /**
   * Get comprehensive performance statistics
   */
  static getPerformanceStats() {
    const cacheStats = CacheManager.getAllStats();
    const performanceStats = globalPerformanceMonitor.getAllStats();
    const slowOperations = globalPerformanceMonitor.getSlowOperations();

    const memoryUsage = process.memoryUsage();
    const systemMetrics = {
      uptime: process.uptime(),
      loadAverage: require("os").loadavg(),
      cpuUsage: process.cpuUsage(),
    };

    const recommendations = this.generatePerformanceRecommendations(
      cacheStats,
      slowOperations,
      memoryUsage,
      systemMetrics
    );

    return {
      cacheStats,
      performanceStats,
      slowOperations,
      memoryUsage,
      systemMetrics,
      recommendations,
    };
  }

  /**
   * Generate performance optimization recommendations
   */
  private static generatePerformanceRecommendations(
    cacheStats: Record<string, any>,
    slowOperations: Array<{ name: string; stats: any }>,
    memoryUsage: NodeJS.MemoryUsage,
    systemMetrics: any
  ): string[] {
    const recommendations: string[] = [];

    // Cache performance recommendations
    Object.entries(cacheStats).forEach(([cacheName, stats]) => {
      if (stats.hitRate < 0.7) {
        recommendations.push(
          `Consider increasing TTL for ${cacheName} cache (current hit rate: ${(
            stats.hitRate * 100
          ).toFixed(1)}%)`
        );
      }
      if (stats.entries > 800) {
        recommendations.push(
          `${cacheName} cache is near capacity (${stats.entries}/1000). Consider increasing maxSize or reducing TTL.`
        );
      }
    });

    // Slow operation recommendations
    slowOperations.forEach(({ name, stats }) => {
      recommendations.push(
        `Consider optimizing ${name} operation (${stats.averageDuration.toFixed(
          2
        )}ms average)`
      );
    });

    // Memory usage recommendations
    if (memoryUsage.heapUsed > 100 * 1024 * 1024) {
      // > 100MB
      recommendations.push(
        "High memory usage detected. Consider implementing memory optimization strategies."
      );
    }

    return recommendations;
  }

  /**
   * Get detailed cache analysis
   */
  static getCacheAnalysis(): {
    summary: {
      totalCaches: number;
      totalEntries: number;
      averageHitRate: number;
      memoryEstimate: number;
    };
    cacheDetails: Array<{
      name: string;
      hitRate: number;
      entries: number;
      memoryUsage: number;
      recommendations: string[];
    }>;
  } {
    const allStats = CacheManager.getAllStats();
    const cacheDetails: any[] = [];
    let totalEntries = 0;
    let totalHitRates = 0;
    let validCaches = 0;

    Object.entries(allStats).forEach(([name, stats]) => {
      totalEntries += stats.entries;
      if (stats.hitRate >= 0) {
        totalHitRates += stats.hitRate;
        validCaches++;
      }

      const recommendations: string[] = [];
      if (stats.hitRate < 0.5) {
        recommendations.push(
          "Consider increasing TTL or reviewing cache strategy"
        );
      }
      if (stats.entries > 800) {
        recommendations.push("Near capacity - consider increasing maxSize");
      }
      if (stats.hitRate > 0.95 && stats.entries < 100) {
        recommendations.push(
          "Excellent performance - consider increasing capacity for more data"
        );
      }

      cacheDetails.push({
        name,
        hitRate: stats.hitRate,
        entries: stats.entries,
        memoryUsage: stats.memoryUsage || 0,
        recommendations,
      });
    });

    return {
      summary: {
        totalCaches: Object.keys(allStats).length,
        totalEntries,
        averageHitRate: validCaches > 0 ? totalHitRates / validCaches : 0,
        memoryEstimate: cacheDetails.reduce(
          (sum, cache) => sum + cache.memoryUsage,
          0
        ),
      },
      cacheDetails: cacheDetails.sort((a, b) => b.hitRate - a.hitRate),
    };
  }
}
