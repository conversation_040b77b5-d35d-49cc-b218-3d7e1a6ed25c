/**
 * Cache entry with expiration and metadata
 */
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * Cache statistics for monitoring
 */
export interface CacheStats {
  hits: number;
  misses: number;
  entries: number;
  hitRate: number;
  memoryUsage: number;
}

/**
 * Cache configuration options
 */
export interface CacheOptions {
  defaultTtl?: number;
  maxSize?: number;
  cleanupInterval?: number;
  enableStats?: boolean;
}

/**
 * Time-based cache with LRU eviction and automatic cleanup
 */
export class TimeBasedCache<T = unknown> {
  private cache = new Map<string, CacheEntry<T>>();
  private stats = {
    hits: 0,
    misses: 0
  };
  
  private readonly defaultTtl: number;
  private readonly maxSize: number;
  private readonly enableStats: boolean;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(options: CacheOptions = {}) {
    this.defaultTtl = options.defaultTtl ?? 300000; // 5 minutes default
    this.maxSize = options.maxSize ?? 1000;
    this.enableStats = options.enableStats ?? true;
    
    // Start cleanup timer
    const cleanupInterval = options.cleanupInterval ?? 60000; // 1 minute default
    this.cleanupTimer = setInterval(() => this.cleanup(), cleanupInterval);
  }

  /**
   * Get a value from the cache
   */
  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    
    if (!entry) {
      if (this.enableStats) this.stats.misses++;
      return undefined;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      if (this.enableStats) this.stats.misses++;
      return undefined;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    if (this.enableStats) this.stats.hits++;
    return entry.value;
  }

  /**
   * Set a value in the cache
   */
  set(key: string, value: T, ttl?: number): void {
    const now = Date.now();
    const entryTtl = ttl ?? this.defaultTtl;
    
    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      ttl: entryTtl,
      accessCount: 0,
      lastAccessed: now
    };

    // If cache is at max size, remove least recently used entry
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    this.cache.set(key, entry);
  }

  /**
   * Check if a key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete a key from the cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all entries from the cache
   */
  clear(): void {
    this.cache.clear();
    if (this.enableStats) {
      this.stats.hits = 0;
      this.stats.misses = 0;
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;
    
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      entries: this.cache.size,
      hitRate,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  /**
   * Get or set a value using a factory function
   */
  async getOrSet<R extends T>(
    key: string,
    factory: () => Promise<R>,
    ttl?: number
  ): Promise<R> {
    const cached = this.get(key) as R;
    if (cached !== undefined) {
      return cached;
    }

    const value = await factory();
    this.set(key, value, ttl);
    return value;
  }

  /**
   * Get all keys in the cache
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get the size of the cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, now)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Check if an entry has expired
   */
  private isExpired(entry: CacheEntry<T>, now = Date.now()): boolean {
    return now - entry.timestamp > entry.ttl;
  }

  /**
   * Evict the least recently used entry
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Estimate memory usage of the cache
   */
  private estimateMemoryUsage(): number {
    let size = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      // Rough estimation: key size + value size + entry overhead
      size += key.length * 2; // UTF-16 characters
      size += this.estimateValueSize(entry.value);
      size += 64; // Estimated overhead for entry object
    }
    
    return size;
  }

  /**
   * Estimate the size of a value in bytes
   */
  private estimateValueSize(value: T): number {
    if (value === null || value === undefined) return 0;
    if (typeof value === 'string') return value.length * 2;
    if (typeof value === 'number') return 8;
    if (typeof value === 'boolean') return 4;
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value).length * 2;
      } catch {
        return 100; // Fallback estimate
      }
    }
    return 50; // Default estimate
  }

  /**
   * Destroy the cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clear();
  }
}

/**
 * Global cache instances for common use cases
 */
export class CacheManager {
  private static instances = new Map<string, TimeBasedCache>();

  /**
   * Get or create a named cache instance
   */
  static getCache<T = unknown>(name: string, options?: CacheOptions): TimeBasedCache<T> {
    if (!this.instances.has(name)) {
      this.instances.set(name, new TimeBasedCache<T>(options));
    }
    return this.instances.get(name) as TimeBasedCache<T>;
  }

  /**
   * Destroy a named cache instance
   */
  static destroyCache(name: string): boolean {
    const cache = this.instances.get(name);
    if (cache) {
      cache.destroy();
      return this.instances.delete(name);
    }
    return false;
  }

  /**
   * Destroy all cache instances
   */
  static destroyAll(): void {
    for (const cache of this.instances.values()) {
      cache.destroy();
    }
    this.instances.clear();
  }

  /**
   * Get statistics for all caches
   */
  static getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {};
    for (const [name, cache] of this.instances.entries()) {
      stats[name] = cache.getStats();
    }
    return stats;
  }
}

// Pre-configured cache instances for common use cases
export const projectCache = CacheManager.getCache('projects', {
  defaultTtl: 300000, // 5 minutes
  maxSize: 100
});

export const fileCache = CacheManager.getCache('files', {
  defaultTtl: 60000, // 1 minute
  maxSize: 500
});

export const commandCache = CacheManager.getCache('commands', {
  defaultTtl: 30000, // 30 seconds
  maxSize: 200
});
