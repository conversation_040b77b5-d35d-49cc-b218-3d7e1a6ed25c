import { globalPerformanceMonitor, PerformanceMonitor } from './performance.js';
import { AdvancedCacheManager } from './advancedCache.js';

/**
 * Performance baseline for regression detection
 */
export interface PerformanceBaseline {
  operationName: string;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  standardDeviation: number;
  sampleSize: number;
  timestamp: Date;
  version: string;
}

/**
 * Performance regression alert
 */
export interface PerformanceRegression {
  operationName: string;
  currentDuration: number;
  baselineDuration: number;
  regressionPercentage: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  context?: Record<string, unknown>;
}

/**
 * Performance trend analysis
 */
export interface PerformanceTrend {
  operationName: string;
  trend: 'improving' | 'stable' | 'degrading';
  changePercentage: number;
  dataPoints: number;
  timespan: number; // milliseconds
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceMonitoringConfig {
  enabled: boolean;
  regressionThresholds: {
    low: number;      // 10% slower
    medium: number;   // 25% slower
    high: number;     // 50% slower
    critical: number; // 100% slower
  };
  baselineUpdateInterval: number; // milliseconds
  alertCallbacks: Array<(regression: PerformanceRegression) => void>;
  trendAnalysisWindow: number; // milliseconds
}

/**
 * Advanced performance regression detector
 */
export class PerformanceRegressionDetector {
  private baselines = new Map<string, PerformanceBaseline>();
  private regressions: PerformanceRegression[] = [];
  private trends = new Map<string, PerformanceTrend>();
  private config: PerformanceMonitoringConfig;
  private cache = AdvancedCacheManager.getCache<PerformanceBaseline>('performance-baselines');

  constructor(config: Partial<PerformanceMonitoringConfig> = {}) {
    this.config = {
      enabled: true,
      regressionThresholds: {
        low: 0.1,      // 10%
        medium: 0.25,  // 25%
        high: 0.5,     // 50%
        critical: 1.0  // 100%
      },
      baselineUpdateInterval: 3600000, // 1 hour
      alertCallbacks: [],
      trendAnalysisWindow: 86400000, // 24 hours
      ...config
    };

    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Record a performance baseline
   */
  async recordBaseline(
    operationName: string,
    version: string = '1.0.0'
  ): Promise<void> {
    const stats = globalPerformanceMonitor.getStats(operationName);
    
    if (!stats || stats.totalCalls < 10) {
      // Need at least 10 samples for a reliable baseline
      return;
    }

    const baseline: PerformanceBaseline = {
      operationName,
      averageDuration: stats.averageDuration,
      minDuration: stats.minDuration,
      maxDuration: stats.maxDuration,
      standardDeviation: this.calculateStandardDeviation(stats.recentMetrics),
      sampleSize: stats.totalCalls,
      timestamp: new Date(),
      version
    };

    this.baselines.set(operationName, baseline);
    
    // Cache the baseline for persistence
    await this.cache.set(operationName, baseline, {
      ttl: this.config.baselineUpdateInterval * 2,
      tags: ['baseline', version],
      metadata: { version, operationName }
    });
  }

  /**
   * Check for performance regressions
   */
  async checkForRegressions(): Promise<PerformanceRegression[]> {
    const regressions: PerformanceRegression[] = [];
    
    for (const [operationName, baseline] of this.baselines.entries()) {
      const currentStats = globalPerformanceMonitor.getStats(operationName);
      
      if (!currentStats || currentStats.totalCalls < 5) {
        continue;
      }

      const regression = this.detectRegression(baseline, currentStats.averageDuration);
      
      if (regression) {
        regressions.push(regression);
        this.regressions.push(regression);
        
        // Trigger alert callbacks
        for (const callback of this.config.alertCallbacks) {
          try {
            callback(regression);
          } catch (error) {
            console.error('Error in performance regression callback:', error);
          }
        }
      }
    }

    return regressions;
  }

  /**
   * Analyze performance trends
   */
  async analyzeTrends(): Promise<PerformanceTrend[]> {
    const trends: PerformanceTrend[] = [];
    const windowStart = Date.now() - this.config.trendAnalysisWindow;

    for (const operationName of globalPerformanceMonitor.getOperationNames()) {
      const stats = globalPerformanceMonitor.getStats(operationName);
      
      if (!stats) continue;

      const recentMetrics = stats.recentMetrics.filter(
        metric => metric.timestamp.getTime() > windowStart
      );

      if (recentMetrics.length < 5) continue;

      const trend = this.calculateTrend(recentMetrics);
      
      if (trend) {
        trends.push({
          operationName,
          ...trend,
          dataPoints: recentMetrics.length,
          timespan: this.config.trendAnalysisWindow
        });
        
        this.trends.set(operationName, {
          operationName,
          ...trend,
          dataPoints: recentMetrics.length,
          timespan: this.config.trendAnalysisWindow
        });
      }
    }

    return trends;
  }

  /**
   * Get performance regression history
   */
  getRegressionHistory(
    operationName?: string,
    since?: Date
  ): PerformanceRegression[] {
    let regressions = this.regressions;

    if (operationName) {
      regressions = regressions.filter(r => r.operationName === operationName);
    }

    if (since) {
      regressions = regressions.filter(r => r.timestamp >= since);
    }

    return regressions.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Get current performance trends
   */
  getCurrentTrends(): PerformanceTrend[] {
    return Array.from(this.trends.values());
  }

  /**
   * Generate performance report
   */
  generateReport(): {
    summary: {
      totalOperations: number;
      operationsWithBaselines: number;
      recentRegressions: number;
      criticalRegressions: number;
    };
    regressions: PerformanceRegression[];
    trends: PerformanceTrend[];
    recommendations: string[];
  } {
    const recentRegressions = this.getRegressionHistory(
      undefined,
      new Date(Date.now() - 86400000) // Last 24 hours
    );

    const criticalRegressions = recentRegressions.filter(
      r => r.severity === 'critical' || r.severity === 'high'
    );

    const recommendations = this.generateRecommendations(recentRegressions);

    return {
      summary: {
        totalOperations: globalPerformanceMonitor.getOperationNames().length,
        operationsWithBaselines: this.baselines.size,
        recentRegressions: recentRegressions.length,
        criticalRegressions: criticalRegressions.length
      },
      regressions: recentRegressions.slice(0, 10), // Last 10 regressions
      trends: this.getCurrentTrends(),
      recommendations
    };
  }

  /**
   * Add alert callback
   */
  addAlertCallback(callback: (regression: PerformanceRegression) => void): void {
    this.config.alertCallbacks.push(callback);
  }

  /**
   * Start monitoring process
   */
  private startMonitoring(): void {
    // Update baselines periodically
    setInterval(() => {
      this.updateBaselines();
    }, this.config.baselineUpdateInterval);

    // Check for regressions periodically
    setInterval(() => {
      this.checkForRegressions();
    }, 60000); // Every minute

    // Analyze trends periodically
    setInterval(() => {
      this.analyzeTrends();
    }, 300000); // Every 5 minutes
  }

  /**
   * Update all baselines
   */
  private async updateBaselines(): Promise<void> {
    const operationNames = globalPerformanceMonitor.getOperationNames();
    
    for (const operationName of operationNames) {
      await this.recordBaseline(operationName);
    }
  }

  /**
   * Detect regression for an operation
   */
  private detectRegression(
    baseline: PerformanceBaseline,
    currentDuration: number
  ): PerformanceRegression | null {
    const regressionRatio = (currentDuration - baseline.averageDuration) / baseline.averageDuration;
    
    if (regressionRatio <= this.config.regressionThresholds.low) {
      return null; // No significant regression
    }

    let severity: PerformanceRegression['severity'] = 'low';
    
    if (regressionRatio >= this.config.regressionThresholds.critical) {
      severity = 'critical';
    } else if (regressionRatio >= this.config.regressionThresholds.high) {
      severity = 'high';
    } else if (regressionRatio >= this.config.regressionThresholds.medium) {
      severity = 'medium';
    }

    return {
      operationName: baseline.operationName,
      currentDuration,
      baselineDuration: baseline.averageDuration,
      regressionPercentage: regressionRatio * 100,
      severity,
      timestamp: new Date(),
      context: {
        baselineVersion: baseline.version,
        baselineSampleSize: baseline.sampleSize,
        baselineTimestamp: baseline.timestamp
      }
    };
  }

  /**
   * Calculate trend for metrics
   */
  private calculateTrend(metrics: Array<{ duration: number; timestamp: Date }>): {
    trend: PerformanceTrend['trend'];
    changePercentage: number;
  } | null {
    if (metrics.length < 5) return null;

    // Simple linear regression to detect trend
    const sortedMetrics = metrics.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const firstHalf = sortedMetrics.slice(0, Math.floor(sortedMetrics.length / 2));
    const secondHalf = sortedMetrics.slice(Math.floor(sortedMetrics.length / 2));

    const firstAvg = firstHalf.reduce((sum, m) => sum + m.duration, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, m) => sum + m.duration, 0) / secondHalf.length;

    const changePercentage = ((secondAvg - firstAvg) / firstAvg) * 100;

    let trend: PerformanceTrend['trend'] = 'stable';
    
    if (Math.abs(changePercentage) > 5) {
      trend = changePercentage > 0 ? 'degrading' : 'improving';
    }

    return { trend, changePercentage };
  }

  /**
   * Calculate standard deviation
   */
  private calculateStandardDeviation(metrics: Array<{ duration: number }>): number {
    if (metrics.length === 0) return 0;

    const mean = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
    const squaredDiffs = metrics.map(m => Math.pow(m.duration - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / metrics.length;
    
    return Math.sqrt(avgSquaredDiff);
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(regressions: PerformanceRegression[]): string[] {
    const recommendations: string[] = [];

    const criticalCount = regressions.filter(r => r.severity === 'critical').length;
    const highCount = regressions.filter(r => r.severity === 'high').length;

    if (criticalCount > 0) {
      recommendations.push(
        `Critical performance regressions detected in ${criticalCount} operations. Immediate investigation required.`
      );
    }

    if (highCount > 0) {
      recommendations.push(
        `High-impact performance regressions detected in ${highCount} operations. Review and optimization recommended.`
      );
    }

    const degradingTrends = this.getCurrentTrends().filter(t => t.trend === 'degrading');
    if (degradingTrends.length > 0) {
      recommendations.push(
        `${degradingTrends.length} operations showing degrading performance trends. Monitor closely.`
      );
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is stable. Continue monitoring.');
    }

    return recommendations;
  }
}

/**
 * Global performance regression detector instance
 */
export const globalRegressionDetector = new PerformanceRegressionDetector();
