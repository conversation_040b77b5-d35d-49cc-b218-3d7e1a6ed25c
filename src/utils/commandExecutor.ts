import { spawn, SpawnOptions } from 'child_process';
import { CommandExecutionError } from './errors.js';

/**
 * Options for command execution
 */
export interface CommandOptions {
  cwd?: string;
  timeout?: number;
  maxBuffer?: number;
  env?: Record<string, string>;
  retries?: number;
  retryDelay?: number;
}

/**
 * Result of command execution
 */
export interface CommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  command: string;
  args: string[];
  duration: number;
}

/**
 * Secure command executor that prevents command injection by using spawn with argument arrays
 */
export class SecureCommandExecutor {
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private static readonly DEFAULT_MAX_BUFFER = 1024 * 1024; // 1MB
  private static readonly DEFAULT_RETRIES = 0;
  private static readonly DEFAULT_RETRY_DELAY = 1000; // 1 second

  /**
   * Execute a command securely using spawn with argument arrays
   */
  static async execute(
    command: string,
    args: string[] = [],
    options: CommandOptions = {}
  ): Promise<CommandResult> {
    const {
      cwd,
      timeout = SecureCommandExecutor.DEFAULT_TIMEOUT,
      maxBuffer = SecureCommandExecutor.DEFAULT_MAX_BUFFER,
      env,
      retries = SecureCommandExecutor.DEFAULT_RETRIES,
      retryDelay = SecureCommandExecutor.DEFAULT_RETRY_DELAY
    } = options;

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        if (attempt > 0) {
          await SecureCommandExecutor.delay(retryDelay * Math.pow(2, attempt - 1)); // Exponential backoff
        }
        
        return await SecureCommandExecutor.executeOnce(command, args, {
          cwd,
          timeout,
          maxBuffer,
          env
        });
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Don't retry on certain types of errors
        if (error instanceof CommandExecutionError) {
          const cmdError = error as CommandExecutionError;
          // Don't retry on permission errors, file not found, etc.
          if (cmdError.exitCode === 127 || cmdError.exitCode === 126) {
            break;
          }
        }
        
        if (attempt === retries) {
          break;
        }
      }
    }
    
    throw lastError || new Error('Command execution failed after retries');
  }

  /**
   * Execute a command once without retries
   */
  private static async executeOnce(
    command: string,
    args: string[],
    options: Omit<CommandOptions, 'retries' | 'retryDelay'>
  ): Promise<CommandResult> {
    const startTime = Date.now();
    
    return new Promise((resolve, reject) => {
      // Validate command and arguments
      if (!command || typeof command !== 'string') {
        reject(new CommandExecutionError('Invalid command', 'Command must be a non-empty string'));
        return;
      }

      // Sanitize arguments
      const sanitizedArgs = args.map(arg => SecureCommandExecutor.sanitizeArgument(arg));

      const spawnOptions: SpawnOptions = {
        cwd: options.cwd,
        env: { ...process.env, ...options.env },
        stdio: ['pipe', 'pipe', 'pipe']
      };

      const child = spawn(command, sanitizedArgs, spawnOptions);
      
      let stdout = '';
      let stderr = '';
      let timeoutId: NodeJS.Timeout | null = null;
      let isTimedOut = false;

      // Set up timeout
      if (options.timeout && options.timeout > 0) {
        timeoutId = setTimeout(() => {
          isTimedOut = true;
          child.kill('SIGTERM');
          
          // Force kill after 5 seconds if still running
          setTimeout(() => {
            if (!child.killed) {
              child.kill('SIGKILL');
            }
          }, 5000);
        }, options.timeout);
      }

      // Handle stdout
      if (child.stdout) {
        child.stdout.on('data', (data: Buffer) => {
          stdout += data.toString();
          if (options.maxBuffer && stdout.length > options.maxBuffer) {
            child.kill('SIGTERM');
            reject(new CommandExecutionError(
              command,
              'Output exceeded maximum buffer size',
              undefined,
              undefined,
              { maxBuffer: options.maxBuffer, actualSize: stdout.length }
            ));
          }
        });
      }

      // Handle stderr
      if (child.stderr) {
        child.stderr.on('data', (data: Buffer) => {
          stderr += data.toString();
          if (options.maxBuffer && stderr.length > options.maxBuffer) {
            child.kill('SIGTERM');
            reject(new CommandExecutionError(
              command,
              'Error output exceeded maximum buffer size',
              undefined,
              undefined,
              { maxBuffer: options.maxBuffer, actualSize: stderr.length }
            ));
          }
        });
      }

      // Handle process completion
      child.on('close', (exitCode: number | null) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        const duration = Date.now() - startTime;
        const result: CommandResult = {
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: exitCode ?? -1,
          command,
          args: sanitizedArgs,
          duration
        };

        if (isTimedOut) {
          reject(new CommandExecutionError(
            command,
            'Command timed out',
            -1,
            undefined,
            { timeout: options.timeout, duration }
          ));
        } else if (exitCode !== 0) {
          reject(new CommandExecutionError(
            command,
            stderr || 'Command failed with non-zero exit code',
            exitCode ?? -1,
            stdout,
            { duration }
          ));
        } else {
          resolve(result);
        }
      });

      // Handle spawn errors
      child.on('error', (error: Error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        reject(new CommandExecutionError(
          command,
          error.message,
          undefined,
          undefined,
          { originalError: error.message }
        ));
      });
    });
  }

  /**
   * Sanitize command arguments to prevent injection
   */
  private static sanitizeArgument(arg: string): string {
    if (typeof arg !== 'string') {
      throw new Error('All arguments must be strings');
    }
    
    // Remove null bytes and other dangerous characters
    return arg.replace(/\0/g, '').trim();
  }

  /**
   * Delay execution for the specified number of milliseconds
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Execute a simple command with string parsing (less secure, use sparingly)
   * This method exists for backward compatibility but should be avoided
   */
  static async executeString(
    commandString: string,
    options: CommandOptions = {}
  ): Promise<CommandResult> {
    // Parse command string into command and arguments
    const parts = SecureCommandExecutor.parseCommandString(commandString);
    if (parts.length === 0) {
      throw new CommandExecutionError(commandString, 'Empty command string');
    }
    
    const [command, ...args] = parts;
    return SecureCommandExecutor.execute(command, args, options);
  }

  /**
   * Parse a command string into command and arguments
   * This is a simple parser and may not handle all edge cases
   */
  private static parseCommandString(commandString: string): string[] {
    const parts: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    
    for (let i = 0; i < commandString.length; i++) {
      const char = commandString[i];
      
      if (!inQuotes && (char === '"' || char === "'")) {
        inQuotes = true;
        quoteChar = char;
      } else if (inQuotes && char === quoteChar) {
        inQuotes = false;
        quoteChar = '';
      } else if (!inQuotes && char === ' ') {
        if (current.trim()) {
          parts.push(current.trim());
          current = '';
        }
      } else {
        current += char;
      }
    }
    
    if (current.trim()) {
      parts.push(current.trim());
    }
    
    return parts;
  }
}

/**
 * Convenience function for executing commands
 */
export async function executeCommand(
  command: string,
  args: string[] = [],
  options: CommandOptions = {}
): Promise<CommandResult> {
  return SecureCommandExecutor.execute(command, args, options);
}

/**
 * Convenience function for executing command strings (less secure)
 */
export async function executeCommandString(
  commandString: string,
  options: CommandOptions = {}
): Promise<CommandResult> {
  return SecureCommandExecutor.executeString(commandString, options);
}
