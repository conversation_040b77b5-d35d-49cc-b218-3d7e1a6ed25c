import * as fs from "fs/promises";
import * as path from "path";
import { PathUtils, CommandUtils, StringUtils } from "./common.js";
import { SecureCommandExecutor } from "./commandExecutor.js";
import { FileOperationError, PathAccessError } from "./errors.js";

/**
 * Consolidated file operation utilities
 */
export class FileHelpers {
  /**
   * Check if a file or directory exists
   */
  static async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats with enhanced error handling
   */
  static async getStats(filePath: string) {
    try {
      return await fs.stat(filePath);
    } catch (error) {
      throw new FileOperationError(
        "stat",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Check if path is a directory
   */
  static async isDirectory(filePath: string): Promise<boolean> {
    try {
      const stats = await this.getStats(filePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * Check if path is a file
   */
  static async isFile(filePath: string): Promise<boolean> {
    try {
      const stats = await this.getStats(filePath);
      return stats.isFile();
    } catch {
      return false;
    }
  }

  /**
   * Format file size in human-readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Get MIME type for file extension
   */
  static getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.json': 'application/json',
      '.js': 'text/javascript',
      '.ts': 'text/typescript',
      '.swift': 'text/x-swift',
      '.m': 'text/x-objc',
      '.h': 'text/x-c',
      '.xml': 'application/xml',
      '.plist': 'application/x-plist',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Read file with enhanced error handling and encoding detection
   */
  static async readFile(
    filePath: string,
    options: {
      encoding?: BufferEncoding;
      asBinary?: boolean;
      maxSize?: number;
    } = {}
  ): Promise<{ content: string; isBinary: boolean; size: number }> {
    const { encoding = 'utf-8', asBinary = false, maxSize = 10 * 1024 * 1024 } = options;

    const stats = await this.getStats(filePath);
    
    if (stats.size > maxSize) {
      throw new FileOperationError(
        "read",
        filePath,
        new Error(`File size (${this.formatFileSize(stats.size)}) exceeds maximum allowed size (${this.formatFileSize(maxSize)})`)
      );
    }

    try {
      if (asBinary) {
        const buffer = await fs.readFile(filePath);
        return {
          content: buffer.toString('base64'),
          isBinary: true,
          size: stats.size
        };
      } else {
        const content = await fs.readFile(filePath, { encoding });
        return {
          content,
          isBinary: false,
          size: stats.size
        };
      }
    } catch (error) {
      throw new FileOperationError(
        "read",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Write file with enhanced error handling
   */
  static async writeFile(
    filePath: string,
    content: string,
    options: {
      encoding?: BufferEncoding;
      fromBase64?: boolean;
      createPath?: boolean;
      backup?: boolean;
    } = {}
  ): Promise<void> {
    const { encoding = 'utf-8', fromBase64 = false, createPath = true, backup = false } = options;

    try {
      // Create directory if needed
      if (createPath) {
        await fs.mkdir(path.dirname(filePath), { recursive: true });
      }

      // Create backup if requested
      if (backup && await this.exists(filePath)) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await fs.copyFile(filePath, backupPath);
      }

      // Write file
      if (fromBase64) {
        const buffer = Buffer.from(content, 'base64');
        await fs.writeFile(filePath, buffer);
      } else {
        await fs.writeFile(filePath, content, { encoding });
      }
    } catch (error) {
      throw new FileOperationError(
        "write",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Find files with enhanced patterns and filtering
   */
  static async findFiles(
    searchPath: string,
    patterns: string | string[],
    options: {
      maxDepth?: number;
      includeHidden?: boolean;
      type?: 'f' | 'd' | 'l';
      timeout?: number;
      caseSensitive?: boolean;
    } = {}
  ): Promise<string[]> {
    const patternsArray = Array.isArray(patterns) ? patterns : [patterns];
    const results: string[] = [];

    for (const pattern of patternsArray) {
      try {
        const files = await CommandUtils.findFiles(searchPath, pattern, options);
        results.push(...files);
      } catch (error) {
        // Continue with other patterns if one fails
        console.warn(`Failed to find files with pattern ${pattern}:`, error);
      }
    }

    return [...new Set(results)].sort();
  }

  /**
   * Search for text within files
   */
  static async searchInFiles(
    searchPath: string,
    searchText: string,
    filePattern: string,
    options: {
      isRegex?: boolean;
      caseSensitive?: boolean;
      maxResults?: number;
      includeHidden?: boolean;
    } = {}
  ): Promise<Array<{
    file: string;
    matches: Array<{ line: number; content: string; match: string }>;
  }>> {
    const { isRegex = false, caseSensitive = false, maxResults = 100, includeHidden = false } = options;

    // Find matching files
    const files = await this.findFiles(searchPath, filePattern, {
      type: 'f',
      includeHidden
    });

    if (files.length === 0) {
      return [];
    }

    // Prepare search regex
    let searchRegex: RegExp;
    if (isRegex) {
      try {
        searchRegex = new RegExp(searchText, caseSensitive ? "g" : "gi");
      } catch (error) {
        throw new Error(`Invalid regular expression: ${
          error instanceof Error ? error.message : String(error)
        }`);
      }
    } else {
      const escapedText = StringUtils.escapeRegExp(searchText);
      searchRegex = new RegExp(escapedText, caseSensitive ? "g" : "gi");
    }

    const results: Array<{
      file: string;
      matches: Array<{ line: number; content: string; match: string }>;
    }> = [];

    let totalMatches = 0;

    for (const file of files) {
      if (totalMatches >= maxResults) break;

      try {
        const { content } = await this.readFile(file, { maxSize: 1024 * 1024 }); // 1MB limit
        const lines = content.split('\n');
        const matches: Array<{ line: number; content: string; match: string }> = [];

        for (let i = 0; i < lines.length && totalMatches < maxResults; i++) {
          const line = lines[i];
          const lineMatches = Array.from(line.matchAll(searchRegex));

          for (const match of lineMatches) {
            if (totalMatches >= maxResults) break;
            
            matches.push({
              line: i + 1,
              content: line,
              match: match[0]
            });
            totalMatches++;
          }
        }

        if (matches.length > 0) {
          results.push({ file, matches });
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }

  /**
   * Copy file or directory with enhanced options
   */
  static async copy(
    source: string,
    destination: string,
    options: {
      recursive?: boolean;
      overwrite?: boolean;
      preserveTimestamps?: boolean;
    } = {}
  ): Promise<void> {
    const { recursive = false, overwrite = false, preserveTimestamps = true } = options;

    const sourceExists = await this.exists(source);
    if (!sourceExists) {
      throw new FileOperationError("copy", source, new Error("Source does not exist"));
    }

    const isSourceDir = await this.isDirectory(source);
    
    if (isSourceDir && !recursive) {
      throw new FileOperationError(
        "copy",
        source,
        new Error("Source is a directory. Use recursive=true to copy directories.")
      );
    }

    const destExists = await this.exists(destination);
    if (destExists && !overwrite) {
      throw new FileOperationError(
        "copy",
        destination,
        new Error("Destination exists and overwrite=false")
      );
    }

    try {
      if (isSourceDir) {
        // Use system cp command for directories
        const args = ['-R'];
        if (preserveTimestamps) args.push('-p');
        args.push(source, destination);
        
        await SecureCommandExecutor.execute('cp', args);
      } else {
        // Use fs.copyFile for files
        await fs.copyFile(source, destination);
        
        if (preserveTimestamps) {
          const stats = await this.getStats(source);
          await fs.utimes(destination, stats.atime, stats.mtime);
        }
      }
    } catch (error) {
      throw new FileOperationError(
        "copy",
        source,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }
}
