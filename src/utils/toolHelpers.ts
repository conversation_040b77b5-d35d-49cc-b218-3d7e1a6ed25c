/**
 * Consolidated utility functions for tool implementations
 * Eliminates duplicate code patterns across tool files
 */

import * as fs from "fs/promises";
import * as path from "path";
import { XcodeServer } from "../server.js";
import { 
  ProjectNotFoundError, 
  PathAccessError, 
  FileOperationError,
  XcodeServerError 
} from "./errors.js";
import { SecureMessageFormatter } from "./common.js";

/**
 * Common validation utilities for tools
 */
export class ToolValidationHelpers {
  /**
   * Ensure active project exists, throw standardized error if not
   */
  static ensureActiveProject(server: XcodeServer): void {
    if (!server.activeProject) {
      throw new ProjectNotFoundError("No active Xcode project detected. Use set_project_path or detect_active_project first.");
    }
  }

  /**
   * Validate and resolve a file path with standardized error handling
   */
  static async validateAndResolvePath(
    server: XcodeServer, 
    inputPath: string, 
    operation: 'read' | 'write' = 'read'
  ): Promise<string> {
    try {
      // Expand tilde and environment variables
      const expandedPath = server.pathManager.expandPath(inputPath);
      
      // Resolve relative paths
      const resolvedPath = server.directoryState.resolvePath(expandedPath);
      
      // Validate path boundaries
      if (operation === 'read') {
        return server.pathManager.validatePathForReading(resolvedPath);
      } else {
        return server.pathManager.validatePathForWriting(resolvedPath);
      }
    } catch (error) {
      if (error instanceof PathAccessError) {
        throw new Error(SecureMessageFormatter.formatError("Access denied", { 
          operation: `${operation}_path`,
          path: inputPath 
        }));
      }
      throw error;
    }
  }

  /**
   * Check if a file exists with standardized error messaging
   */
  static async ensureFileExists(filePath: string, fileType = "file"): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        throw new FileOperationError("validate", filePath, new Error(`Path is not a ${fileType}`));
      }
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        throw new FileOperationError("access", filePath, new Error(`${fileType} not found`));
      }
      throw error;
    }
  }

  /**
   * Check if a directory exists with standardized error messaging
   */
  static async ensureDirectoryExists(dirPath: string, dirType = "directory"): Promise<void> {
    try {
      const stats = await fs.stat(dirPath);
      if (!stats.isDirectory()) {
        throw new FileOperationError("validate", dirPath, new Error(`Path is not a ${dirType}`));
      }
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        throw new FileOperationError("access", dirPath, new Error(`${dirType} not found`));
      }
      throw error;
    }
  }

  /**
   * Validate app bundle (.app directory)
   */
  static async validateAppBundle(appPath: string): Promise<void> {
    if (!appPath.endsWith(".app")) {
      throw new Error("Path must end with .app extension");
    }
    
    try {
      const stats = await fs.stat(appPath);
      if (!stats.isDirectory()) {
        throw new Error("App bundle must be a directory");
      }
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        throw new Error("App bundle not found");
      }
      throw error;
    }
  }

  /**
   * Validate file extension
   */
  static validateFileExtension(filePath: string, expectedExtensions: string[]): void {
    const ext = path.extname(filePath).toLowerCase();
    if (!expectedExtensions.includes(ext)) {
      throw new Error(`File must have one of these extensions: ${expectedExtensions.join(", ")}`);
    }
  }

  /**
   * Get active directory with validation
   */
  static getValidatedActiveDirectory(server: XcodeServer): string {
    const activeDirectory = server.directoryState.getActiveDirectory();
    server.pathManager.validatePathForReading(activeDirectory);
    return activeDirectory;
  }

  /**
   * Check for required files in a directory (e.g., Podfile, Package.swift)
   */
  static async checkRequiredFiles(
    directory: string, 
    requiredFiles: string[], 
    projectType: string
  ): Promise<void> {
    const missingFiles: string[] = [];
    
    for (const file of requiredFiles) {
      const filePath = path.join(directory, file);
      try {
        await fs.access(filePath);
      } catch {
        missingFiles.push(file);
      }
    }
    
    if (missingFiles.length > 0) {
      throw new XcodeServerError(
        `Missing required ${projectType} files: ${missingFiles.join(", ")}. ` +
        `This project doesn't appear to use ${projectType}.`
      );
    }
  }
}

/**
 * Common project information utilities
 */
export class ProjectInfoHelpers {
  /**
   * Get project info with caching and error handling
   */
  static async getProjectInfoSafely(server: XcodeServer): Promise<any> {
    ToolValidationHelpers.ensureActiveProject(server);
    
    const cacheKey = `project_info_${server.activeProject!.path}`;
    return server.cache.getOrSet(
      cacheKey,
      async () => {
        const { getProjectInfo, getWorkspaceInfo } = await import("./project.js");
        
        if (server.activeProject!.isWorkspace) {
          return getWorkspaceInfo(server.activeProject!.path, server.commandExecutor);
        } else {
          return getProjectInfo(server.activeProject!.path, server.commandExecutor);
        }
      },
      300000 // 5 minutes TTL
    );
  }

  /**
   * Validate scheme exists in project
   */
  static async validateScheme(server: XcodeServer, scheme: string): Promise<void> {
    const projectInfo = await this.getProjectInfoSafely(server);
    if (!projectInfo.schemes.includes(scheme)) {
      throw new XcodeServerError(
        `Invalid scheme "${scheme}". Available schemes: ${projectInfo.schemes.join(", ")}`
      );
    }
  }

  /**
   * Validate configuration exists in project
   */
  static async validateConfiguration(server: XcodeServer, configuration: string): Promise<void> {
    const projectInfo = await this.getProjectInfoSafely(server);
    if (!projectInfo.configurations.includes(configuration)) {
      throw new XcodeServerError(
        `Invalid configuration "${configuration}". Available configurations: ${projectInfo.configurations.join(", ")}`
      );
    }
  }

  /**
   * Validate both scheme and configuration
   */
  static async validateSchemeAndConfiguration(
    server: XcodeServer, 
    scheme: string, 
    configuration: string
  ): Promise<any> {
    const projectInfo = await this.getProjectInfoSafely(server);
    
    if (!projectInfo.schemes.includes(scheme)) {
      throw new XcodeServerError(
        `Invalid scheme "${scheme}". Available schemes: ${projectInfo.schemes.join(", ")}`
      );
    }
    
    if (!projectInfo.configurations.includes(configuration)) {
      throw new XcodeServerError(
        `Invalid configuration "${configuration}". Available configurations: ${projectInfo.configurations.join(", ")}`
      );
    }
    
    return projectInfo;
  }
}

/**
 * Common command execution patterns
 */
export class CommandHelpers {
  /**
   * Execute command with standardized error handling
   */
  static async executeWithErrorHandling(
    server: XcodeServer,
    command: string,
    args: string[],
    options: { timeout?: number; cwd?: string } = {},
    errorContext?: string
  ): Promise<{ stdout: string; stderr: string }> {
    try {
      return await server.commandExecutor.execute(command, args, {
        timeout: options.timeout || 30000,
        cwd: options.cwd
      });
    } catch (error) {
      const context = errorContext || `${command} ${args.join(" ")}`;
      throw new Error(SecureMessageFormatter.formatError(
        `Command execution failed: ${context}`,
        { 
          command,
          error: error instanceof Error ? error.message : String(error)
        }
      ));
    }
  }

  /**
   * Execute command and return formatted result
   */
  static async executeAndFormatResult(
    server: XcodeServer,
    command: string,
    args: string[],
    successMessage: string,
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<{ content: Array<{ type: "text"; text: string }> }> {
    const result = await this.executeWithErrorHandling(server, command, args, options);
    
    const formattedOutput = SecureMessageFormatter.formatCommandOutput(
      result.stdout, 
      result.stderr
    );
    
    return {
      content: [{
        type: "text",
        text: SecureMessageFormatter.formatSuccess(
          successMessage,
          formattedOutput
        )
      }]
    };
  }
}

/**
 * Common error handling patterns
 */
export class ErrorHandlers {
  /**
   * Handle path access errors consistently
   */
  static handlePathError(error: unknown, operation: string): never {
    if (error instanceof PathAccessError) {
      throw new Error(SecureMessageFormatter.formatError("Access denied", { operation }));
    }
    throw error;
  }

  /**
   * Handle file operation errors consistently
   */
  static handleFileError(error: unknown, filePath: string, operation: string): never {
    if (error instanceof FileOperationError) {
      throw new Error(SecureMessageFormatter.formatError(
        `File operation failed: ${operation}`,
        { operation }
      ));
    }
    throw error;
  }

  /**
   * Create standardized tool result
   */
  static createResult(message: string, data?: unknown): { content: Array<{ type: "text"; text: string }> } {
    return {
      content: [{
        type: "text",
        text: SecureMessageFormatter.formatSuccess(message, data)
      }]
    };
  }

  /**
   * Create standardized error result
   */
  static createErrorResult(error: Error | string): { content: Array<{ type: "text"; text: string }> } {
    const message = error instanceof Error ? error.message : error;
    return {
      content: [{
        type: "text",
        text: SecureMessageFormatter.formatError(message)
      }]
    };
  }
}
