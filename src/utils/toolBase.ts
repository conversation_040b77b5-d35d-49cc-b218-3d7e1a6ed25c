import { z } from "zod";
import { XcodeServer } from "../server.js";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./toolFactory.js";
import { SecureMessageFormatter } from "./common.js";
import { globalPerformanceMonitor } from "./performance.js";

/**
 * Base class for tool implementations providing common patterns
 */
export abstract class ToolBase<TParams = any> {
  protected server: XcodeServer;
  protected toolName: string;
  protected description: string;
  protected schema: z.ZodSchema<TParams>;

  constructor(
    server: XcodeServer,
    toolName: string,
    description: string,
    schema: z.ZodSchema<TParams>
  ) {
    this.server = server;
    this.toolName = toolName;
    this.description = description;
    this.schema = schema;
  }

  /**
   * Abstract method that must be implemented by subclasses
   */
  protected abstract executeImpl(params: TParams): Promise<ToolResult>;

  /**
   * Validate parameters using the schema
   */
  protected validateParams(params: unknown): TParams {
    try {
      return this.schema.parse(params);
    } catch (error) {
      throw new Error(
        `Parameter validation failed for ${this.toolName}: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Standard path validation and resolution
   */
  protected async validateAndResolvePath(
    inputPath: string,
    operation: "read" | "write" = "read"
  ): Promise<string> {
    // Expand tilde and environment variables
    const expandedPath = this.server.pathManager.expandPath(inputPath);

    // Resolve relative paths
    const resolvedPath = this.server.directoryState.resolvePath(expandedPath);

    // Validate path boundaries
    if (operation === "read") {
      return this.server.pathManager.validatePathForReading(resolvedPath);
    } else {
      return this.server.pathManager.validatePathForWriting(resolvedPath);
    }
  }

  /**
   * Standard command execution with error handling
   */
  protected async executeCommand(
    command: string,
    args: string[],
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<{ stdout: string; stderr: string }> {
    try {
      return await this.server.commandExecutor.execute(command, args, {
        timeout: options.timeout || 30000,
        cwd: options.cwd,
      });
    } catch (error) {
      throw new Error(
        SecureMessageFormatter.formatError(
          `Command execution failed: ${command} ${args.join(" ")}`,
          {
            command,
            args,
            error: error instanceof Error ? error.message : String(error),
          }
        )
      );
    }
  }

  /**
   * Create a standardized success response
   */
  protected createSuccessResponse(message: string, data?: unknown): ToolResult {
    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatSuccess(message, data),
        },
      ],
    };
  }

  /**
   * Create a standardized error response
   */
  protected createErrorResponse(message: string, error?: Error): ToolResult {
    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatError(message, {
            error: error?.message,
            timestamp: new Date().toISOString(),
          }),
        },
      ],
      isError: true,
    };
  }

  /**
   * Execute the tool with standardized error handling and performance monitoring
   */
  async execute(params: unknown): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate parameters
      const validatedParams = this.validateParams(params);

      // Execute the tool implementation
      const result = await this.executeImpl(validatedParams);

      // Record performance metrics
      globalPerformanceMonitor.recordMetric({
        name: this.toolName,
        duration: Date.now() - startTime,
        timestamp: new Date(),
        metadata: { success: true },
      });

      return result;
    } catch (error) {
      // Record failed operation
      globalPerformanceMonitor.recordMetric({
        name: this.toolName,
        duration: Date.now() - startTime,
        timestamp: new Date(),
        metadata: {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        },
      });

      // Return standardized error response
      return this.createErrorResponse(
        `Tool execution failed: ${this.toolName}`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Register this tool with the server
   */
  register(): void {
    this.server.server.tool(
      this.toolName,
      this.description,
      this.schema as any,
      this.execute.bind(this) as any
    );
  }
}

/**
 * Base class for file operation tools
 */
export abstract class FileToolBase<TParams = any> extends ToolBase<TParams> {
  /**
   * Check if a file exists
   */
  protected async fileExists(filePath: string): Promise<boolean> {
    try {
      const fs = await import("fs/promises");
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats with error handling
   */
  protected async getFileStats(filePath: string) {
    try {
      const fs = await import("fs/promises");
      return await fs.stat(filePath);
    } catch (error) {
      throw new Error(
        `Cannot access file ${filePath}: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
}

/**
 * Base class for project operation tools
 */
export abstract class ProjectToolBase<TParams = any> extends ToolBase<TParams> {
  /**
   * Ensure active project exists
   */
  protected ensureActiveProject(): void {
    if (!this.server.activeProject) {
      throw new Error(
        "No active project set. Use set_project_path to set an active project first."
      );
    }
  }

  /**
   * Get active project directory
   */
  protected getActiveProjectDirectory(): string {
    this.ensureActiveProject();
    return this.server.directoryState.getActiveDirectory();
  }

  /**
   * Validate project-related path
   */
  protected async validateProjectPath(inputPath: string): Promise<string> {
    const resolvedPath = await this.validateAndResolvePath(inputPath, "read");

    // Ensure path is within project boundaries
    const projectDir = this.getActiveProjectDirectory();
    if (!resolvedPath.startsWith(projectDir)) {
      throw new Error(
        `Path ${resolvedPath} is outside the active project directory`
      );
    }

    return resolvedPath;
  }
}

/**
 * Base class for command-line tool wrappers
 */
export abstract class CommandToolBase<TParams = any> extends ToolBase<TParams> {
  protected abstract getCommand(): string;
  protected abstract buildArgs(params: TParams): string[];

  /**
   * Execute the command with standardized handling
   */
  protected async executeToolCommand(
    params: TParams,
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<{ stdout: string; stderr: string }> {
    const command = this.getCommand();
    const args = this.buildArgs(params);

    return this.executeCommand(command, args, options);
  }
}
