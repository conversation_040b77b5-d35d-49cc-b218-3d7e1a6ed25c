/**
 * Tool categories for better organization and management
 */
export enum ToolCategory {
  PROJECT = 'project',
  FILE = 'file', 
  BUILD = 'build',
  PACKAGE_MANAGEMENT = 'package-management',
  SIMULATOR = 'simulator',
  XCODE_UTILITIES = 'xcode-utilities',
  DEVELOPMENT = 'development'
}

/**
 * Tool metadata for enhanced organization
 */
export interface ToolMetadata {
  category: ToolCategory;
  description: string;
  tags: string[];
  complexity: 'simple' | 'intermediate' | 'advanced';
  requiresActiveProject: boolean;
  requiresXcode: boolean;
  platforms: ('ios' | 'macos' | 'watchos' | 'tvos')[];
  version: string;
}

/**
 * Tool registry for centralized tool management
 */
export class ToolRegistry {
  private static tools = new Map<string, ToolMetadata>();

  /**
   * Register a tool with metadata
   */
  static register(name: string, metadata: ToolMetadata): void {
    this.tools.set(name, metadata);
  }

  /**
   * Get tool metadata
   */
  static getMetadata(name: string): ToolMetadata | undefined {
    return this.tools.get(name);
  }

  /**
   * Get tools by category
   */
  static getByCategory(category: ToolCategory): Array<{ name: string; metadata: ToolMetadata }> {
    const result: Array<{ name: string; metadata: ToolMetadata }> = [];
    
    for (const [name, metadata] of this.tools.entries()) {
      if (metadata.category === category) {
        result.push({ name, metadata });
      }
    }
    
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get all registered tools
   */
  static getAll(): Array<{ name: string; metadata: ToolMetadata }> {
    return Array.from(this.tools.entries()).map(([name, metadata]) => ({ name, metadata }));
  }

  /**
   * Search tools by tags
   */
  static searchByTags(tags: string[]): Array<{ name: string; metadata: ToolMetadata }> {
    const result: Array<{ name: string; metadata: ToolMetadata }> = [];
    
    for (const [name, metadata] of this.tools.entries()) {
      const hasMatchingTag = tags.some(tag => 
        metadata.tags.some(metaTag => 
          metaTag.toLowerCase().includes(tag.toLowerCase())
        )
      );
      
      if (hasMatchingTag) {
        result.push({ name, metadata });
      }
    }
    
    return result;
  }

  /**
   * Get tools by complexity level
   */
  static getByComplexity(complexity: 'simple' | 'intermediate' | 'advanced'): Array<{ name: string; metadata: ToolMetadata }> {
    const result: Array<{ name: string; metadata: ToolMetadata }> = [];
    
    for (const [name, metadata] of this.tools.entries()) {
      if (metadata.complexity === complexity) {
        result.push({ name, metadata });
      }
    }
    
    return result;
  }

  /**
   * Generate tool documentation
   */
  static generateDocumentation(): string {
    const categories = Object.values(ToolCategory);
    let doc = '# Xcode MCP Server Tools\n\n';
    
    for (const category of categories) {
      const tools = this.getByCategory(category);
      if (tools.length === 0) continue;
      
      doc += `## ${category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}\n\n`;
      
      for (const { name, metadata } of tools) {
        doc += `### ${name}\n`;
        doc += `- **Description**: ${metadata.description}\n`;
        doc += `- **Complexity**: ${metadata.complexity}\n`;
        doc += `- **Requires Active Project**: ${metadata.requiresActiveProject ? 'Yes' : 'No'}\n`;
        doc += `- **Requires Xcode**: ${metadata.requiresXcode ? 'Yes' : 'No'}\n`;
        doc += `- **Platforms**: ${metadata.platforms.join(', ')}\n`;
        doc += `- **Tags**: ${metadata.tags.join(', ')}\n\n`;
      }
    }
    
    return doc;
  }
}

/**
 * Tool category mappings for migration
 */
export const TOOL_CATEGORY_MAPPINGS: Record<string, ToolCategory> = {
  // Project tools
  'set_projects_base_dir': ToolCategory.PROJECT,
  'set_project_path': ToolCategory.PROJECT,
  'get_active_project': ToolCategory.PROJECT,
  'find_projects': ToolCategory.PROJECT,
  'detect_active_project': ToolCategory.PROJECT,
  'get_project_configuration': ToolCategory.PROJECT,
  'add_file_to_project': ToolCategory.PROJECT,
  'create_workspace': ToolCategory.PROJECT,
  'add_project_to_workspace': ToolCategory.PROJECT,
  'create_xcode_project': ToolCategory.PROJECT,
  
  // File tools
  'read_file': ToolCategory.FILE,
  'write_file': ToolCategory.FILE,
  'copy_file': ToolCategory.FILE,
  'move_file': ToolCategory.FILE,
  'delete_file': ToolCategory.FILE,
  'create_directory': ToolCategory.FILE,
  'list_project_files': ToolCategory.FILE,
  'list_directory': ToolCategory.FILE,
  'get_file_info': ToolCategory.FILE,
  'find_files': ToolCategory.FILE,
  'resolve_path': ToolCategory.FILE,
  'check_file_exists': ToolCategory.FILE,
  'search_in_files': ToolCategory.FILE,
  
  // Build tools
  'analyze_file': ToolCategory.BUILD,
  'build_project': ToolCategory.BUILD,
  'run_tests': ToolCategory.BUILD,
  'list_available_destinations': ToolCategory.BUILD,
  'list_available_schemes': ToolCategory.BUILD,
  'clean_project': ToolCategory.BUILD,
  'archive_project': ToolCategory.BUILD,
  
  // Package management
  'pod_install': ToolCategory.PACKAGE_MANAGEMENT,
  'pod_update': ToolCategory.PACKAGE_MANAGEMENT,
  'pod_outdated': ToolCategory.PACKAGE_MANAGEMENT,
  'pod_repo_update': ToolCategory.PACKAGE_MANAGEMENT,
  'pod_deintegrate': ToolCategory.PACKAGE_MANAGEMENT,
  'check_cocoapods': ToolCategory.PACKAGE_MANAGEMENT,
  'pod_init': ToolCategory.PACKAGE_MANAGEMENT,
  'init_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'add_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'remove_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'edit_package_swift': ToolCategory.PACKAGE_MANAGEMENT,
  'build_spm_package': ToolCategory.PACKAGE_MANAGEMENT,
  'test_spm_package': ToolCategory.PACKAGE_MANAGEMENT,
  'get_package_info': ToolCategory.PACKAGE_MANAGEMENT,
  'update_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'swift_package_command': ToolCategory.PACKAGE_MANAGEMENT,
  'build_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'test_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'show_swift_dependencies': ToolCategory.PACKAGE_MANAGEMENT,
  'clean_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'dump_swift_package': ToolCategory.PACKAGE_MANAGEMENT,
  'generate_swift_docs': ToolCategory.PACKAGE_MANAGEMENT,
  
  // Simulator tools
  'list_booted_simulators': ToolCategory.SIMULATOR,
  'list_simulators': ToolCategory.SIMULATOR,
  'boot_simulator': ToolCategory.SIMULATOR,
  'shutdown_simulator': ToolCategory.SIMULATOR,
  'install_app': ToolCategory.SIMULATOR,
  'launch_app': ToolCategory.SIMULATOR,
  'terminate_app': ToolCategory.SIMULATOR,
  'open_url': ToolCategory.SIMULATOR,
  'take_screenshot': ToolCategory.SIMULATOR,
  'reset_simulator': ToolCategory.SIMULATOR,
  'list_installed_apps': ToolCategory.SIMULATOR,
  
  // Xcode utilities
  'run_xcrun': ToolCategory.XCODE_UTILITIES,
  'compile_asset_catalog': ToolCategory.XCODE_UTILITIES,
  'run_lldb': ToolCategory.XCODE_UTILITIES,
  'trace_app': ToolCategory.XCODE_UTILITIES,
  'get_xcode_info': ToolCategory.XCODE_UTILITIES,
  'switch_xcode': ToolCategory.XCODE_UTILITIES,
  'export_archive': ToolCategory.XCODE_UTILITIES,
  'validate_app': ToolCategory.XCODE_UTILITIES,
  'generate_icon_set': ToolCategory.XCODE_UTILITIES,
};

/**
 * Initialize tool registry with default metadata
 */
export function initializeToolRegistry(): void {
  // This will be populated as tools are migrated to use the registry
  console.log('Tool registry initialized');
}
