import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "../testFramework.js";
import { PathUtils, StringUtils, CommandUtils } from "../../utils/common.js";
import * as os from "os";
import * as path from "path";

/**
 * Test suite for PathUtils
 */
const pathUtilsTests: TestSuite = {
  name: "PathUtils",
  description: "Tests for path manipulation utilities",
  tests: [
    {
      name: "expandPath should handle tilde expansion",
      fn: () => {
        const result = PathUtils.expandPath("~/test");
        const expected = path.join(os.homedir(), "test");
        Assert.equal(result, expected);
      },
      tags: ["path", "utils"],
    },
    {
      name: "expandPath should handle environment variables",
      fn: () => {
        process.env.TEST_VAR = "test_value";
        const result = PathUtils.expandPath("$TEST_VAR/path");
        Assert.truthy(result.includes("test_value"));
        delete process.env.TEST_VAR;
      },
      tags: ["path", "utils", "env"],
    },
    {
      name: "expandPath should handle ${VAR} format",
      fn: () => {
        process.env.TEST_VAR2 = "test_value2";
        const result = PathUtils.expandPath("${TEST_VAR2}/path");
        Assert.truthy(result.includes("test_value2"));
        delete process.env.TEST_VAR2;
      },
      tags: ["path", "utils", "env"],
    },
    {
      name: "isPathWithinBoundaries should validate path boundaries",
      fn: () => {
        const allowedPaths = ["/allowed/path1", "/allowed/path2"];

        Assert.truthy(
          PathUtils.isPathWithinBoundaries(
            "/allowed/path1/subdir",
            allowedPaths
          )
        );
        Assert.falsy(
          PathUtils.isPathWithinBoundaries("/forbidden/path", allowedPaths)
        );
      },
      tags: ["path", "security"],
    },
    {
      name: "safeRelativePath should prevent directory traversal",
      fn: () => {
        Assert.throws(() => {
          PathUtils.safeRelativePath("/base", "/outside");
        }, "outside");
      },
      tags: ["path", "security"],
    },
  ],
};

/**
 * Test suite for StringUtils
 */
const stringUtilsTests: TestSuite = {
  name: "StringUtils",
  description: "Tests for string manipulation utilities",
  tests: [
    {
      name: "escapeRegExp should escape special characters",
      fn: () => {
        const input = "test.with*special+chars?";
        const result = StringUtils.escapeRegExp(input);
        Assert.equal(result, "test\\.with\\*special\\+chars\\?");
      },
      tags: ["string", "regex"],
    },
    {
      name: "truncate should limit string length",
      fn: () => {
        const input = "This is a very long string that should be truncated";
        const result = StringUtils.truncate(input, 20);
        Assert.equal(result.length, 20); // maxLength includes suffix
        Assert.truthy(result.endsWith("..."));
      },
      tags: ["string", "formatting"],
    },
    {
      name: "capitalize should capitalize first letter",
      fn: () => {
        Assert.equal(StringUtils.capitalize("hello"), "Hello");
        Assert.equal(StringUtils.capitalize("HELLO"), "Hello");
        Assert.equal(StringUtils.capitalize(""), "");
      },
      tags: ["string", "formatting"],
    },
    {
      name: "camelCase should convert to camelCase",
      fn: () => {
        Assert.equal(StringUtils.camelCase("hello-world"), "helloWorld");
        Assert.equal(StringUtils.camelCase("hello_world"), "helloWorld");
        Assert.equal(StringUtils.camelCase("hello world"), "helloWorld");
      },
      tags: ["string", "formatting"],
    },
    {
      name: "kebabCase should convert to kebab-case",
      fn: () => {
        Assert.equal(StringUtils.kebabCase("helloWorld"), "hello-world");
        Assert.equal(StringUtils.kebabCase("hello_world"), "hello-world");
        Assert.equal(StringUtils.kebabCase("Hello World"), "hello-world");
      },
      tags: ["string", "formatting"],
    },
  ],
};

/**
 * Test suite for CommandUtils
 */
const commandUtilsTests: TestSuite = {
  name: "CommandUtils",
  description: "Tests for command execution utilities",
  tests: [
    {
      name: "parseCommandOutput should parse lines correctly",
      fn: () => {
        const output = "line1\nline2\n\nline4\n";
        const result = CommandUtils.parseCommandOutput(output);
        Assert.equal(result.length, 3);
        Assert.equal(result[0], "line1");
        Assert.equal(result[1], "line2");
        Assert.equal(result[2], "line4");
      },
      tags: ["command", "parsing"],
    },
    {
      name: "parseCommandOutput should handle empty lines when configured",
      fn: () => {
        const output = "line1\n\nline3\n";
        const result = CommandUtils.parseCommandOutput(output, {
          filterEmpty: false,
        });
        Assert.equal(result.length, 4);
        Assert.equal(result[1], "");
      },
      tags: ["command", "parsing"],
    },
    {
      name: "findProjectFiles should handle multiple extensions",
      async fn() {
        // Mock the findFiles method
        const originalFindFiles = CommandUtils.findFiles;
        CommandUtils.findFiles = Mock.fn(
          async (searchPath: string, pattern: string) => {
            if (pattern === "*.swift")
              return ["/path/file1.swift", "/path/file2.swift"];
            if (pattern === "*.ts") return ["/path/file1.ts"];
            return [];
          }
        );

        try {
          const result = await CommandUtils.findProjectFiles("/test", [
            "swift",
            "ts",
          ]);
          Assert.equal(result.length, 3);
          Assert.contains(result, "/path/file1.swift");
          Assert.contains(result, "/path/file2.swift");
          Assert.contains(result, "/path/file1.ts");
        } finally {
          CommandUtils.findFiles = originalFindFiles;
        }
      },
      tags: ["command", "files"],
    },
  ],
};

/**
 * Run all tests
 */
export async function runCommonUtilsTests(): Promise<void> {
  const runner = new TestRunner({
    verbose: true,
    outputFormat: "console",
    timeout: 10000,
  });

  runner.addSuite(pathUtilsTests);
  runner.addSuite(stringUtilsTests);
  runner.addSuite(commandUtilsTests);

  const results = await runner.run();

  if (results.failed > 0) {
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCommonUtilsTests().catch(console.error);
}
