import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Test result status
 */
export type TestStatus = 'pass' | 'fail' | 'skip' | 'pending';

/**
 * Test case interface
 */
export interface TestCase {
  name: string;
  description?: string;
  fn: () => Promise<void> | void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
  tags?: string[];
}

/**
 * Test result interface
 */
export interface TestResult {
  name: string;
  status: TestStatus;
  duration: number;
  error?: Error;
  output?: string;
  tags?: string[];
}

/**
 * Test suite interface
 */
export interface TestSuite {
  name: string;
  description?: string;
  tests: TestCase[];
  beforeEach?: () => Promise<void> | void;
  afterEach?: () => Promise<void> | void;
  beforeAll?: () => Promise<void> | void;
  afterAll?: () => Promise<void> | void;
  tags?: string[];
}

/**
 * Test runner configuration
 */
export interface TestRunnerConfig {
  timeout: number;
  parallel: boolean;
  maxConcurrency: number;
  bail: boolean; // Stop on first failure
  verbose: boolean;
  tags?: string[]; // Run only tests with these tags
  pattern?: string; // Run only tests matching pattern
  outputFormat: 'console' | 'json' | 'junit';
  outputFile?: string;
}

/**
 * Assertion utilities
 */
export class Assert {
  static equal<T>(actual: T, expected: T, message?: string): void {
    if (actual !== expected) {
      throw new Error(
        message || `Expected ${expected}, but got ${actual}`
      );
    }
  }

  static deepEqual<T>(actual: T, expected: T, message?: string): void {
    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
      throw new Error(
        message || `Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`
      );
    }
  }

  static throws(fn: () => void, expectedError?: string | RegExp, message?: string): void {
    let threw = false;
    let error: Error | undefined;

    try {
      fn();
    } catch (e) {
      threw = true;
      error = e instanceof Error ? e : new Error(String(e));
    }

    if (!threw) {
      throw new Error(message || 'Expected function to throw an error');
    }

    if (expectedError && error) {
      if (typeof expectedError === 'string') {
        if (!error.message.includes(expectedError)) {
          throw new Error(
            message || `Expected error message to contain "${expectedError}", but got "${error.message}"`
          );
        }
      } else if (expectedError instanceof RegExp) {
        if (!expectedError.test(error.message)) {
          throw new Error(
            message || `Expected error message to match ${expectedError}, but got "${error.message}"`
          );
        }
      }
    }
  }

  static async throwsAsync(
    fn: () => Promise<void>,
    expectedError?: string | RegExp,
    message?: string
  ): Promise<void> {
    let threw = false;
    let error: Error | undefined;

    try {
      await fn();
    } catch (e) {
      threw = true;
      error = e instanceof Error ? e : new Error(String(e));
    }

    if (!threw) {
      throw new Error(message || 'Expected async function to throw an error');
    }

    if (expectedError && error) {
      if (typeof expectedError === 'string') {
        if (!error.message.includes(expectedError)) {
          throw new Error(
            message || `Expected error message to contain "${expectedError}", but got "${error.message}"`
          );
        }
      } else if (expectedError instanceof RegExp) {
        if (!expectedError.test(error.message)) {
          throw new Error(
            message || `Expected error message to match ${expectedError}, but got "${error.message}"`
          );
        }
      }
    }
  }

  static truthy(value: unknown, message?: string): void {
    if (!value) {
      throw new Error(message || `Expected truthy value, but got ${value}`);
    }
  }

  static falsy(value: unknown, message?: string): void {
    if (value) {
      throw new Error(message || `Expected falsy value, but got ${value}`);
    }
  }

  static isInstanceOf<T>(
    value: unknown,
    constructor: new (...args: any[]) => T,
    message?: string
  ): void {
    if (!(value instanceof constructor)) {
      throw new Error(
        message || `Expected instance of ${constructor.name}, but got ${typeof value}`
      );
    }
  }

  static contains<T>(array: T[], item: T, message?: string): void {
    if (!array.includes(item)) {
      throw new Error(
        message || `Expected array to contain ${item}, but it didn't`
      );
    }
  }

  static hasProperty(obj: object, property: string, message?: string): void {
    if (!(property in obj)) {
      throw new Error(
        message || `Expected object to have property "${property}"`
      );
    }
  }
}

/**
 * Mock utilities
 */
export class Mock {
  private originalValue: any;
  private mockValue: any;
  private calls: any[][] = [];

  constructor(private target: any, private property: string) {
    this.originalValue = target[property];
  }

  /**
   * Mock with a function
   */
  mockImplementation(implementation: (...args: any[]) => any): this {
    this.mockValue = (...args: any[]) => {
      this.calls.push(args);
      return implementation(...args);
    };
    this.target[this.property] = this.mockValue;
    return this;
  }

  /**
   * Mock with a return value
   */
  mockReturnValue(value: any): this {
    return this.mockImplementation(() => value);
  }

  /**
   * Mock with resolved promise
   */
  mockResolvedValue(value: any): this {
    return this.mockImplementation(() => Promise.resolve(value));
  }

  /**
   * Mock with rejected promise
   */
  mockRejectedValue(error: any): this {
    return this.mockImplementation(() => Promise.reject(error));
  }

  /**
   * Get call count
   */
  getCallCount(): number {
    return this.calls.length;
  }

  /**
   * Get call arguments
   */
  getCallArgs(callIndex: number = 0): any[] {
    return this.calls[callIndex] || [];
  }

  /**
   * Get all calls
   */
  getAllCalls(): any[][] {
    return [...this.calls];
  }

  /**
   * Check if mock was called
   */
  wasCalled(): boolean {
    return this.calls.length > 0;
  }

  /**
   * Check if mock was called with specific arguments
   */
  wasCalledWith(...args: any[]): boolean {
    return this.calls.some(call => 
      call.length === args.length && 
      call.every((arg, index) => arg === args[index])
    );
  }

  /**
   * Reset mock
   */
  reset(): this {
    this.calls = [];
    return this;
  }

  /**
   * Restore original value
   */
  restore(): void {
    this.target[this.property] = this.originalValue;
    this.calls = [];
  }

  /**
   * Create a mock function
   */
  static fn(implementation?: (...args: any[]) => any): Mock & ((...args: any[]) => any) {
    const calls: any[][] = [];
    
    const mockFn = (...args: any[]) => {
      calls.push(args);
      return implementation ? implementation(...args) : undefined;
    };

    // Add mock methods to the function
    Object.assign(mockFn, {
      getCallCount: () => calls.length,
      getCallArgs: (index: number = 0) => calls[index] || [],
      getAllCalls: () => [...calls],
      wasCalled: () => calls.length > 0,
      wasCalledWith: (...args: any[]) => 
        calls.some(call => 
          call.length === args.length && 
          call.every((arg, index) => arg === args[index])
        ),
      reset: () => { calls.length = 0; return mockFn; },
      mockReturnValue: (value: any) => {
        return Mock.fn(() => value);
      },
      mockResolvedValue: (value: any) => {
        return Mock.fn(() => Promise.resolve(value));
      },
      mockRejectedValue: (error: any) => {
        return Mock.fn(() => Promise.reject(error));
      }
    });

    return mockFn as any;
  }
}

/**
 * Test runner class
 */
export class TestRunner {
  private suites: TestSuite[] = [];
  private config: TestRunnerConfig;

  constructor(config: Partial<TestRunnerConfig> = {}) {
    this.config = {
      timeout: 5000,
      parallel: false,
      maxConcurrency: 4,
      bail: false,
      verbose: false,
      outputFormat: 'console',
      ...config
    };
  }

  /**
   * Add test suite
   */
  addSuite(suite: TestSuite): void {
    this.suites.push(suite);
  }

  /**
   * Run all tests
   */
  async run(): Promise<{
    passed: number;
    failed: number;
    skipped: number;
    total: number;
    results: TestResult[];
    duration: number;
  }> {
    const startTime = Date.now();
    const results: TestResult[] = [];
    
    for (const suite of this.suites) {
      if (this.config.verbose) {
        console.log(`\nRunning suite: ${suite.name}`);
      }

      // Filter tests based on configuration
      const testsToRun = this.filterTests(suite.tests);

      // Run beforeAll hook
      if (suite.beforeAll) {
        try {
          await suite.beforeAll();
        } catch (error) {
          console.error(`beforeAll failed for suite ${suite.name}:`, error);
          continue;
        }
      }

      // Run tests
      const suiteResults = this.config.parallel
        ? await this.runTestsParallel(testsToRun, suite)
        : await this.runTestsSequential(testsToRun, suite);

      results.push(...suiteResults);

      // Run afterAll hook
      if (suite.afterAll) {
        try {
          await suite.afterAll();
        } catch (error) {
          console.error(`afterAll failed for suite ${suite.name}:`, error);
        }
      }

      // Bail on first failure if configured
      if (this.config.bail && suiteResults.some(r => r.status === 'fail')) {
        break;
      }
    }

    const duration = Date.now() - startTime;
    const summary = this.generateSummary(results, duration);

    // Output results
    await this.outputResults(summary);

    return summary;
  }

  /**
   * Filter tests based on configuration
   */
  private filterTests(tests: TestCase[]): TestCase[] {
    let filtered = tests;

    // Filter by tags
    if (this.config.tags && this.config.tags.length > 0) {
      filtered = filtered.filter(test => 
        test.tags?.some(tag => this.config.tags!.includes(tag))
      );
    }

    // Filter by pattern
    if (this.config.pattern) {
      const regex = new RegExp(this.config.pattern, 'i');
      filtered = filtered.filter(test => regex.test(test.name));
    }

    // Handle 'only' tests
    const onlyTests = filtered.filter(test => test.only);
    if (onlyTests.length > 0) {
      filtered = onlyTests;
    }

    return filtered;
  }

  /**
   * Run tests sequentially
   */
  private async runTestsSequential(
    tests: TestCase[],
    suite: TestSuite
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];

    for (const test of tests) {
      const result = await this.runSingleTest(test, suite);
      results.push(result);

      if (this.config.verbose) {
        this.logTestResult(result);
      }

      if (this.config.bail && result.status === 'fail') {
        break;
      }
    }

    return results;
  }

  /**
   * Run tests in parallel
   */
  private async runTestsParallel(
    tests: TestCase[],
    suite: TestSuite
  ): Promise<TestResult[]> {
    const chunks = this.chunkArray(tests, this.config.maxConcurrency);
    const results: TestResult[] = [];

    for (const chunk of chunks) {
      const chunkResults = await Promise.all(
        chunk.map(test => this.runSingleTest(test, suite))
      );
      
      results.push(...chunkResults);

      if (this.config.verbose) {
        chunkResults.forEach(result => this.logTestResult(result));
      }

      if (this.config.bail && chunkResults.some(r => r.status === 'fail')) {
        break;
      }
    }

    return results;
  }

  /**
   * Run a single test
   */
  private async runSingleTest(test: TestCase, suite: TestSuite): Promise<TestResult> {
    if (test.skip) {
      return {
        name: test.name,
        status: 'skip',
        duration: 0,
        tags: test.tags
      };
    }

    const startTime = Date.now();
    let output = '';
    
    // Capture console output
    const originalLog = console.log;
    console.log = (...args) => {
      output += args.join(' ') + '\n';
    };

    try {
      // Run beforeEach hook
      if (suite.beforeEach) {
        await suite.beforeEach();
      }

      // Run the test with timeout
      const timeout = test.timeout || this.config.timeout;
      await Promise.race([
        Promise.resolve(test.fn()),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error(`Test timeout after ${timeout}ms`)), timeout)
        )
      ]);

      // Run afterEach hook
      if (suite.afterEach) {
        await suite.afterEach();
      }

      return {
        name: test.name,
        status: 'pass',
        duration: Date.now() - startTime,
        output: output.trim() || undefined,
        tags: test.tags
      };
    } catch (error) {
      return {
        name: test.name,
        status: 'fail',
        duration: Date.now() - startTime,
        error: error instanceof Error ? error : new Error(String(error)),
        output: output.trim() || undefined,
        tags: test.tags
      };
    } finally {
      console.log = originalLog;
    }
  }

  /**
   * Generate test summary
   */
  private generateSummary(results: TestResult[], duration: number) {
    const passed = results.filter(r => r.status === 'pass').length;
    const failed = results.filter(r => r.status === 'fail').length;
    const skipped = results.filter(r => r.status === 'skip').length;

    return {
      passed,
      failed,
      skipped,
      total: results.length,
      results,
      duration
    };
  }

  /**
   * Log test result
   */
  private logTestResult(result: TestResult): void {
    const status = result.status === 'pass' ? '✓' : 
                  result.status === 'fail' ? '✗' : 
                  result.status === 'skip' ? '-' : '?';
    
    console.log(`  ${status} ${result.name} (${result.duration}ms)`);
    
    if (result.error) {
      console.log(`    Error: ${result.error.message}`);
    }
  }

  /**
   * Output test results
   */
  private async outputResults(summary: any): Promise<void> {
    if (this.config.outputFormat === 'console') {
      this.outputConsole(summary);
    } else if (this.config.outputFormat === 'json' && this.config.outputFile) {
      await this.outputJson(summary);
    } else if (this.config.outputFormat === 'junit' && this.config.outputFile) {
      await this.outputJUnit(summary);
    }
  }

  /**
   * Output to console
   */
  private outputConsole(summary: any): void {
    console.log('\n' + '='.repeat(50));
    console.log(`Tests: ${summary.passed} passed, ${summary.failed} failed, ${summary.skipped} skipped`);
    console.log(`Total: ${summary.total} tests in ${summary.duration}ms`);
    
    if (summary.failed > 0) {
      console.log('\nFailed tests:');
      summary.results
        .filter((r: TestResult) => r.status === 'fail')
        .forEach((r: TestResult) => {
          console.log(`  - ${r.name}: ${r.error?.message}`);
        });
    }
  }

  /**
   * Output to JSON file
   */
  private async outputJson(summary: any): Promise<void> {
    if (!this.config.outputFile) return;
    
    await fs.writeFile(
      this.config.outputFile,
      JSON.stringify(summary, null, 2),
      'utf-8'
    );
  }

  /**
   * Output to JUnit XML file
   */
  private async outputJUnit(summary: any): Promise<void> {
    if (!this.config.outputFile) return;
    
    // Simple JUnit XML generation
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += `<testsuite tests="${summary.total}" failures="${summary.failed}" skipped="${summary.skipped}" time="${summary.duration / 1000}">\n`;
    
    for (const result of summary.results) {
      xml += `  <testcase name="${result.name}" time="${result.duration / 1000}"`;
      
      if (result.status === 'fail') {
        xml += '>\n';
        xml += `    <failure message="${result.error?.message || 'Test failed'}">${result.error?.stack || ''}</failure>\n`;
        xml += '  </testcase>\n';
      } else if (result.status === 'skip') {
        xml += '>\n    <skipped/>\n  </testcase>\n';
      } else {
        xml += '/>\n';
      }
    }
    
    xml += '</testsuite>\n';
    
    await fs.writeFile(this.config.outputFile, xml, 'utf-8');
  }

  /**
   * Utility to chunk array
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}
