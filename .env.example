# Xcode MCP Server - Example Configuration

# Set the base directory where your Xcode projects are stored
# This helps the server find and manage your projects
# Example: PROJECTS_BASE_DIR=/Users/<USER>/Documents/XcodeProjects
PROJECTS_BASE_DIR=

# Enable debug logging (true/false)
# Setting this to true will output more detailed logs to help with troubleshooting
DEBUG=false

# Maximum number of files to cache in memory
# Increasing this can improve performance but will use more memory
# MAX_CACHED_FILES=100

# Server Configuration
SERVER_NAME=xcode-server
SERVER_VERSION=1.0.0 