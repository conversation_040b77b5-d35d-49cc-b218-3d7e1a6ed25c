// Simple test script to validate our path handling fixes

import * as path from "path";
import { getProjectInfo, getWorkspaceInfo } from "../src/utils/project.js";
import { SecureCommandExecutor } from "../src/utils/commandExecutor.js";

/**
 * Test various project path formats
 */
async function testProjectPaths() {
  console.error("Testing project path handling...");

  // Test cases with various path formats
  const testCases = [
    {
      description: "Standard .xcodeproj path",
      path: "/path/to/TestProject.xcodeproj",
    },
    {
      description: "Standard .xcworkspace path",
      path: "/path/to/TestProject.xcworkspace",
    },
    {
      description: "project.xcworkspace inside .xcodeproj",
      path: "/path/to/TestProject.xcodeproj/project.xcworkspace",
    },
  ];

  // Mock the SecureCommandExecutor to verify command construction
  const originalExecute = SecureCommandExecutor.execute;

  SecureCommandExecutor.execute = async (
    command: string,
    args: string[] = []
  ) => {
    console.error(
      `Command that would be executed: ${command} ${args.join(" ")}`
    );
    // Return mock data so the function can continue
    return {
      stdout:
        "Mock stdout\nTargets:\nMockTarget\nBuild Configurations:\nDebug\nRelease\nSchemes:\nMockScheme",
      stderr: "",
    };
  };

  // Test getProjectInfo with each case
  console.error("\n--- Testing getProjectInfo ---");
  for (const testCase of testCases) {
    console.error(`\nTesting: ${testCase.description}`);
    try {
      // We don't need the actual result, we just want to see what command is constructed
      await getProjectInfo(testCase.path, SecureCommandExecutor);
    } catch (error) {
      console.error(`Error: ${(error as any).message}`);
    }
  }

  // Test getWorkspaceInfo with each case
  console.error("\n--- Testing getWorkspaceInfo ---");
  for (const testCase of testCases) {
    console.error(`\nTesting: ${testCase.description}`);
    try {
      // We don't need the actual result, we just want to see what command is constructed
      await getWorkspaceInfo(testCase.path, SecureCommandExecutor);
    } catch (error) {
      console.error(`Error: ${(error as any).message}`);
    }
  }

  // Restore original execute method
  SecureCommandExecutor.execute = originalExecute;
}

// Run the tests
testProjectPaths()
  .then(() => console.error("\nTests completed"))
  .catch((error) => console.error(`Test failed: ${error.message}`));
