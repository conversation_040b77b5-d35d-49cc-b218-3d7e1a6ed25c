#!/usr/bin/env ts-node

import * as fs from 'fs/promises';
import * as path from 'path';
import { PathUtils, StringUtils } from '../src/utils/common.js';

/**
 * Tool migration script to help transition existing tools to new base classes
 */

interface ToolInfo {
  name: string;
  filePath: string;
  category: string;
  complexity: 'simple' | 'intermediate' | 'advanced';
  requiresActiveProject: boolean;
  requiresXcode: boolean;
  platforms: string[];
}

class ToolMigrator {
  private toolsDir = path.join(process.cwd(), 'src/tools');
  private outputDir = path.join(process.cwd(), 'src/tools/migrated');

  async run(): Promise<void> {
    console.log('🚀 Starting tool migration process...\n');

    try {
      // Ensure output directory exists
      await fs.mkdir(this.outputDir, { recursive: true });

      // Discover existing tools
      const tools = await this.discoverTools();
      console.log(`📋 Found ${tools.length} tools to migrate:\n`);

      for (const tool of tools) {
        console.log(`  - ${tool.name} (${tool.category})`);
      }

      console.log('\n🔄 Starting migration...\n');

      // Migrate each tool
      for (const tool of tools) {
        await this.migrateTool(tool);
      }

      // Generate migration report
      await this.generateMigrationReport(tools);

      console.log('\n✅ Migration completed successfully!');
      console.log(`📁 Migrated tools are available in: ${this.outputDir}`);
      console.log('📊 Check migration-report.md for detailed information');

    } catch (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
  }

  private async discoverTools(): Promise<ToolInfo[]> {
    const tools: ToolInfo[] = [];
    const categories = await fs.readdir(this.toolsDir, { withFileTypes: true });

    for (const category of categories) {
      if (!category.isDirectory() || category.name === 'enhanced' || category.name === 'migrated') {
        continue;
      }

      const categoryPath = path.join(this.toolsDir, category.name);
      const indexPath = path.join(categoryPath, 'index.ts');

      try {
        const content = await fs.readFile(indexPath, 'utf-8');
        const toolNames = this.extractToolNames(content);

        for (const toolName of toolNames) {
          tools.push({
            name: toolName,
            filePath: indexPath,
            category: category.name,
            complexity: this.inferComplexity(content, toolName),
            requiresActiveProject: this.checkRequiresActiveProject(content, toolName),
            requiresXcode: this.checkRequiresXcode(content, toolName),
            platforms: this.inferPlatforms(content, toolName)
          });
        }
      } catch (error) {
        console.warn(`⚠️  Could not process ${indexPath}:`, error);
      }
    }

    return tools;
  }

  private extractToolNames(content: string): string[] {
    const toolNames: string[] = [];
    
    // Look for server.tool() calls
    const toolRegex = /server\.tool\(\s*["']([^"']+)["']/g;
    let match;
    
    while ((match = toolRegex.exec(content)) !== null) {
      toolNames.push(match[1]);
    }

    return toolNames;
  }

  private inferComplexity(content: string, toolName: string): 'simple' | 'intermediate' | 'advanced' {
    // Simple heuristics based on content analysis
    const lines = content.split('\n').length;
    const hasAsyncOperations = content.includes('await') || content.includes('Promise');
    const hasComplexLogic = content.includes('for (') || content.includes('while (') || content.includes('switch (');
    
    if (lines > 200 || (hasAsyncOperations && hasComplexLogic)) {
      return 'advanced';
    } else if (lines > 100 || hasAsyncOperations) {
      return 'intermediate';
    } else {
      return 'simple';
    }
  }

  private checkRequiresActiveProject(content: string, toolName: string): boolean {
    return content.includes('activeProject') || content.includes('ensureActiveProject');
  }

  private checkRequiresXcode(content: string, toolName: string): boolean {
    return content.includes('xcodebuild') || content.includes('xcrun') || content.includes('Xcode');
  }

  private inferPlatforms(content: string, toolName: string): string[] {
    const platforms: string[] = [];
    
    if (content.includes('ios') || content.includes('iPhone') || content.includes('iPad')) {
      platforms.push('ios');
    }
    if (content.includes('macos') || content.includes('macOS')) {
      platforms.push('macos');
    }
    if (content.includes('watchos') || content.includes('watchOS')) {
      platforms.push('watchos');
    }
    if (content.includes('tvos') || content.includes('tvOS')) {
      platforms.push('tvos');
    }

    // Default to all platforms if none detected
    if (platforms.length === 0) {
      platforms.push('ios', 'macos', 'watchos', 'tvos');
    }

    return platforms;
  }

  private async migrateTool(tool: ToolInfo): Promise<void> {
    console.log(`🔧 Migrating ${tool.name}...`);

    try {
      const originalContent = await fs.readFile(tool.filePath, 'utf-8');
      const migratedContent = this.generateMigratedTool(tool, originalContent);
      
      const outputPath = path.join(this.outputDir, `${tool.category}-${tool.name}.ts`);
      await fs.writeFile(outputPath, migratedContent, 'utf-8');
      
      console.log(`  ✅ Migrated to ${outputPath}`);
    } catch (error) {
      console.error(`  ❌ Failed to migrate ${tool.name}:`, error);
    }
  }

  private generateMigratedTool(tool: ToolInfo, originalContent: string): string {
    const className = StringUtils.capitalize(StringUtils.camelCase(tool.name)) + 'Tool';
    const baseClass = this.selectBaseClass(tool);
    
    return `import { z } from "zod";
import { ${baseClass} } from "../../utils/toolBase.js";
import { ToolResult } from "../../utils/toolFactory.js";
import { XcodeServer } from "../../server.js";
import { ToolCategory, ToolRegistry } from "../categories.js";

/**
 * Migrated ${tool.name} tool using base class pattern
 * 
 * Original implementation from: ${tool.filePath}
 * Migration date: ${new Date().toISOString()}
 */
export class ${className} extends ${baseClass}<{
  // TODO: Define parameter interface based on original schema
  [key: string]: any;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      '${tool.name}',
      'TODO: Add description from original implementation',
      z.object({
        // TODO: Migrate schema from original implementation
      })
    );

    // Register with tool registry
    ToolRegistry.register(this.toolName, {
      category: ToolCategory.${tool.category.toUpperCase()},
      description: this.description,
      tags: ['${tool.category}', 'migrated'],
      complexity: '${tool.complexity}',
      requiresActiveProject: ${tool.requiresActiveProject},
      requiresXcode: ${tool.requiresXcode},
      platforms: [${tool.platforms.map(p => `'${p}'`).join(', ')}],
      version: '2.0.0'
    });
  }

  protected async executeImpl(params: any): Promise<ToolResult> {
    try {
      // TODO: Migrate implementation from original tool
      // Original implementation:
      /*
${this.commentOriginalCode(originalContent)}
      */
      
      return this.createSuccessResponse(
        'Tool migrated successfully - implementation needed',
        { migrated: true, originalTool: '${tool.name}' }
      );
    } catch (error) {
      return this.createErrorResponse(
        \`Failed to execute \${this.toolName}\`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }
}

/**
 * Register migrated ${tool.name} tool
 */
export function register${className}(server: XcodeServer): void {
  const tool = new ${className}(server);
  tool.register();
}`;
  }

  private selectBaseClass(tool: ToolInfo): string {
    if (tool.category === 'file') {
      return 'FileToolBase';
    } else if (tool.category === 'project') {
      return 'ProjectToolBase';
    } else if (tool.category === 'build' || tool.category === 'xcode') {
      return 'CommandToolBase';
    } else {
      return 'ToolBase';
    }
  }

  private commentOriginalCode(content: string): string {
    return content
      .split('\n')
      .map(line => `      // ${line}`)
      .join('\n');
  }

  private async generateMigrationReport(tools: ToolInfo[]): Promise<void> {
    const reportPath = path.join(this.outputDir, 'migration-report.md');
    
    let report = `# Tool Migration Report

Generated: ${new Date().toISOString()}

## Summary

- **Total Tools**: ${tools.length}
- **Categories**: ${[...new Set(tools.map(t => t.category))].join(', ')}
- **Complexity Distribution**:
  - Simple: ${tools.filter(t => t.complexity === 'simple').length}
  - Intermediate: ${tools.filter(t => t.complexity === 'intermediate').length}
  - Advanced: ${tools.filter(t => t.complexity === 'advanced').length}

## Migration Status

| Tool Name | Category | Complexity | Requires Project | Requires Xcode | Status |
|-----------|----------|------------|------------------|----------------|--------|
`;

    for (const tool of tools) {
      report += `| ${tool.name} | ${tool.category} | ${tool.complexity} | ${tool.requiresActiveProject ? '✅' : '❌'} | ${tool.requiresXcode ? '✅' : '❌'} | 🔄 Migrated |\n`;
    }

    report += `

## Next Steps

1. **Review Migrated Tools**: Check each migrated tool in \`src/tools/migrated/\`
2. **Complete Implementation**: Fill in TODO comments with actual implementation
3. **Update Schemas**: Migrate parameter schemas from original tools
4. **Test Migration**: Run tests to ensure functionality is preserved
5. **Update Registration**: Replace original tool registration with migrated versions

## Migration Guidelines

### Base Class Selection

- **FileToolBase**: For file system operations
- **ProjectToolBase**: For project-specific operations
- **CommandToolBase**: For command-line tool wrappers
- **ToolBase**: For general-purpose tools

### Implementation Checklist

- [ ] Parameter schema migrated
- [ ] Core logic migrated
- [ ] Error handling updated
- [ ] Performance monitoring enabled
- [ ] Tool registry metadata complete
- [ ] Tests updated
- [ ] Documentation updated

## Automated Improvements

The migrated tools automatically include:

- ✅ Standardized error handling
- ✅ Performance monitoring
- ✅ Parameter validation
- ✅ Consistent logging
- ✅ Tool registry integration
- ✅ Enhanced caching support
- ✅ Security improvements

## Manual Tasks Required

- 🔧 Complete TODO comments in migrated files
- 🔧 Test migrated functionality
- 🔧 Update tool registration in main server
- 🔧 Remove original tool implementations
- 🔧 Update documentation
`;

    await fs.writeFile(reportPath, report, 'utf-8');
    console.log(`📊 Migration report generated: ${reportPath}`);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  const migrator = new ToolMigrator();
  migrator.run().catch(console.error);
}
